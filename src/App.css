#root {
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  text-align: center;
  width: 100%;
}

.logo {
  height: 2em;
  padding: 1em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
/* 导航栏样式 */
.nav  {
    display: flex;
    align-items: center; /* 垂直居中对齐 */
    justify-content: space-between;
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* padding: 1rem 2rem; */
    border-radius: 8px;
}
  
.nav .logo {
font-size: 1.25rem;
font-weight: bold;
color: #2563eb;
line-height: 1; /* 确保文字垂直居中 */
}

.nav ul {
display: flex;
list-style: none;
margin: 0;
padding: 0;
}

.nav ul li {
margin: 0;
}

.nav ul li .nav-link {
display: inline-block;
padding: 0rem 1rem;
font-size: 1rem;
color: #374151;
text-decoration: none;
border-radius: 4px;
transition: all 0.3s ease;
}

.nav ul li .nav-link:hover {
background-color: #2563eb; /* 蓝色背景 */
color: #ffffff; /* 白色文字 */
}

.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f0f2f5;
}

.login-card {
    width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.auth-switch {
    text-align: center;
}

.nav-user {
    margin-left: auto;
}

.user-profile-dropdown {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.user-profile-dropdown:hover {
    background-color: #f5f5f5;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}
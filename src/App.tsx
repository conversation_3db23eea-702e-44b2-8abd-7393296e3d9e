import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link, Navigate } from 'react-router-dom';

import CreateEvaluationTask from './pages/CreateEvaluationTask';
import EvaluationPlatform from './pages/EvaluationPlatform';
import EvaluationSetDetail from './pages/EvaluationSetDetail';
import QuantitativeCompare from './pages/QuantitativeCompare';
import ImageCompare from './pages/ImageCompare';
import TaskProgressPage from './pages/TaskProgressPage';
import PdpPathAnnotation from './pages/PdpPathAnnotation';
import DlpPathAnnotation from './pages/DlpPathAnnotation';
import PdpLaneSceneAnnotation from './pages/PdpLaneSceneAnnotation'; // 添加导入
//import PdpClipPathAnnotation from './pages/PdpClipPathAnnotation';
import PathPairAnnotation from './pages/path_pair/PathPairAnnotation';  // 新增
import PathPairStatistics from './pages/path_pair/PathPairStatistics';  // 新增
import PathPairComparison from './pages/path_pair/PathPairComparison';
import ValidityCheckTaskAssignmentPage from './pages/validity_check/TaskAssignmentPage';
import ValidityCheckTaskManagementPage from './pages/validity_check/TaskManagementPage';
import AnnotationPage from './pages/validity_check/AnnotationPage';
import AnnotationProgress from './pages/AnnotationProgress';
import CombinedPathPairAnnotation from './pages/path_pair/CombinedPathPairAnnotation';
import LoginForm from './components/LoginForm';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { Spin } from 'antd';
import UserProfile from './components/UserProfile';
import './App.css';
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { isAuthenticated, loading } = useAuth();

    if (loading) {
        return <div className="loading-container"><Spin size="large" /></div>;
    }

    if (!isAuthenticated) {
        return <Navigate to="/login" replace />;
    }

    return <>{children}</>;
};

const AppContent: React.FC = () => {
    const { user, isAuthenticated, login, logout, loading } = useAuth();
    if (loading) {
        return <div className="loading-container"><Spin size="large" tip="正在验证用户身份..." /></div>;
    }
    if (!isAuthenticated) {
        return (
            <Routes>
                <Route path="/login" element={<LoginForm onLoginSuccess={(userData, token) => login(userData, token)} />} />
                <Route path="*" element={<Navigate to="/login" replace />} />
            </Routes>
        );
    }

    return (
        <>
            {/* 导航菜单 */}
            <nav className="nav">
                <div className="logo">
                    评测平台
                </div>
                <ul>
                    <li>
                        <Link to="/" className="nav-link">
                            首页
                        </Link>
                    </li>
                    <li>
                        <Link to="/create-task" className="nav-link">
                            创建评测任务
                        </Link>
                    </li>
                    <li>
                        <Link to="/annotation-progress" className="nav-link">
                            标注进展
                        </Link>
                    </li>
                    <li>
                        <Link to="/validity-check/management" className="nav-link">
                            任务管理
                        </Link>
                    </li>
                    <li>
                        <Link to="/validity-check/assignment" className="nav-link">
                            我的任务
                        </Link>
                    </li>
                    <li>
                        <Link to="/combined-path-pair-annotation" className="nav-link">
                            组合标注
                        </Link>
                    </li>
                </ul>
                <div className="nav-user">
                    <UserProfile user={user!} onLogout={logout} />
                </div>
            </nav>

            <Routes>
                <Route path="/" element={<ProtectedRoute><EvaluationPlatform /></ProtectedRoute>} />
                <Route path="/create-task" element={<ProtectedRoute><CreateEvaluationTask /></ProtectedRoute>} />
                <Route path="/annotation-progress" element={<ProtectedRoute><AnnotationProgress /></ProtectedRoute>} />
                <Route path="/evaluation-sets/:id" element={<ProtectedRoute><EvaluationSetDetail /></ProtectedRoute>} />
                <Route path="/evaluation_sets/:id/quantitative_compare" element={<ProtectedRoute><QuantitativeCompare /></ProtectedRoute>} />
                <Route path="/evaluation_sets/:id/image_compare" element={<ProtectedRoute><ImageCompare /></ProtectedRoute>} />
                <Route path="/task-progress" element={<ProtectedRoute><TaskProgressPage /></ProtectedRoute>} />
                
                {/* PDP 相关标注页面 - 统一使用pdp前缀 */}
                <Route path="/pdp-annotation/:id" element={<ProtectedRoute><PdpPathAnnotation /></ProtectedRoute>} />
                <Route path="/dlp-annotation/:id" element={<ProtectedRoute><DlpPathAnnotation /></ProtectedRoute>} />
                <Route path="/pdp-lane-scene-annotation/:id" element={<ProtectedRoute><PdpLaneSceneAnnotation /></ProtectedRoute>} />
                
                {/* Path Pair 相关页面 */}
                <Route path="/path-pair-annotation/:id" element={<ProtectedRoute><PathPairAnnotation /></ProtectedRoute>} />
                <Route path="/path-pair-statistics/:id" element={<ProtectedRoute><PathPairStatistics /></ProtectedRoute>} />
                <Route path="/path-pair-comparison/:evaluationSetId/:pklId" element={<ProtectedRoute><PathPairComparison /></ProtectedRoute>} />
                <Route path="/combined-path-pair-annotation" element={<ProtectedRoute><CombinedPathPairAnnotation /></ProtectedRoute>} />
                

                {/* Validity Check 相关路由 */}
                <Route path="/validity-check/management" element={<ProtectedRoute><ValidityCheckTaskManagementPage /></ProtectedRoute>} />
                <Route path="/validity-check/assignment" element={<ProtectedRoute><ValidityCheckTaskAssignmentPage /></ProtectedRoute>} />
                <Route path="/validity-check/annotation/:taskId/:assignmentId" element={<ProtectedRoute><AnnotationPage /></ProtectedRoute>} />

                {/* 其他路由 */}
                <Route path="/login" element={<Navigate to="/" replace />} />
            </Routes>
        </>
    );
};

function App() {
    return (
        <AuthProvider>
            <Router>
                <div className="p-6 min-h-screen bg-gray-50">
                    <AppContent />
                </div>
            </Router>
        </AuthProvider>
    );
}


export default App;

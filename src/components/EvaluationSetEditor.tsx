import React, { useState, useEffect } from 'react';
import {
    Modal,
    Tabs,
    Table,
    Button,
    Upload,
    message,
    Spin,
    Input,
    Checkbox,
    Popconfirm,
    Space,
    Card
} from 'antd';
import {
    UploadOutlined,
    DeleteOutlined,
    SearchOutlined,
    DownloadOutlined
} from '@ant-design/icons';
import axios from 'axios';
import { EvaluationSet } from '../types';

const { TabPane } = Tabs;
const { Search } = Input;

interface EvaluationSetEditorProps {
    visible: boolean;
    evaluationSet: EvaluationSet;
    onClose: () => void;
    onSuccess: () => void;
}

interface CaseItem {
    id: number;
    pkl_name: string;
    pkl_dir: string;
    vehicle_type: string;
    vin: string;
    time_ns: number;
    key_obs_id: number;
    dirty_data: boolean;
}

const EvaluationSetEditor: React.FC<EvaluationSetEditorProps> = ({
    visible,
    evaluationSet,
    onClose,
    onSuccess
}) => {
    const [loading, setLoading] = useState(false);
    const [cases, setCases] = useState<CaseItem[]>([]);
    const [selectedCaseIds, setSelectedCaseIds] = useState<number[]>([]);
    const [searchKeyword, setSearchKeyword] = useState('');
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0
    });

    // 获取评测集中的案例
    const fetchCases = async (page = 1, search = '') => {
        setLoading(true);
        try {
            const response = await axios.get(`/api/evaluation_sets/${evaluationSet.id}`, {
                params: {
                    page,
                    per_page: pagination.pageSize,
                    search: search || undefined
                }
            });

            if (response.data.success) {
                setCases(response.data.cases);
                setPagination(prev => ({
                    ...prev,
                    current: response.data.page,
                    total: response.data.case_count
                }));
            } else {
                message.error('获取案例失败: ' + response.data.error);
            }
        } catch (error) {
            message.error('请求出错');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (visible) {
            fetchCases(1);
        }
    }, [visible]);

    // 删除选中的案例
    const handleDeleteSelected = async () => {
        if (selectedCaseIds.length === 0) {
            message.warning('请先选择要删除的案例');
            return;
        }

        setLoading(true);
        try {
            const response = await axios.delete(`/api/evaluation_sets/${evaluationSet.id}/cases`, {
                data: { case_ids: selectedCaseIds }
            });

            if (response.data.success) {
                message.success(`成功删除 ${selectedCaseIds.length} 个案例`);
                setSelectedCaseIds([]);
                fetchCases(pagination.current, searchKeyword);
            } else {
                message.error('删除失败: ' + response.data.error);
            }
        } catch (error) {
            message.error('删除请求出错');
        } finally {
            setLoading(false);
        }
    };

    // 搜索案例
    const handleSearch = (value: string) => {
        setSearchKeyword(value);
        fetchCases(1, value);
    };

    // 分页变化
    const handlePageChange = (page: number) => {
        fetchCases(page, searchKeyword);
    };

    // CSV上传处理
    const handleUploadCSV = async (file: File) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('evaluation_set_id', evaluationSet.id.toString());

        setLoading(true);
        try {
            const response = await axios.post(`/api/evaluation_sets/${evaluationSet.id}/upload_csv`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            if (response.data.success) {
                message.success(response.data.message);
                fetchCases(1, searchKeyword);
            } else {
                message.error('上传失败: ' + response.data.error);
            }
        } catch (error) {
            message.error('上传请求出错');
        } finally {
            setLoading(false);
        }

        return false; // 阻止默认上传行为
    };

    // 表格列定义
    const columns = [
        {
            title: 'PKL文件名',
            dataIndex: 'pkl_name',
            key: 'pkl_name',
            width: 300,
            ellipsis: true,
        },
        {
            title: '脏数据',
            dataIndex: 'dirty_data',
            key: 'dirty_data',
            width: 80,
            render: (dirty: boolean) => dirty ? '是' : '否',
        },
    ];

    const rowSelection = {
        selectedRowKeys: selectedCaseIds,
        onChange: (selectedRowKeys: React.Key[]) => {
            setSelectedCaseIds(selectedRowKeys as number[]);
        },
        onSelectAll: (selected: boolean, selectedRows: CaseItem[], changeRows: CaseItem[]) => {
            if (selected) {
                setSelectedCaseIds(cases.map(item => item.id));
            } else {
                setSelectedCaseIds([]);
            }
        },
    };

    return (
        <Modal
            title={`编辑评测集: ${evaluationSet.set_name}`}
            open={visible}
            onCancel={onClose}
            footer={[
                <Button key="close" onClick={onClose}>
                    关闭
                </Button>
            ]}
            width={1200}
            style={{ top: 20 }}
        >
            <Tabs defaultActiveKey="cases">
                <TabPane tab="案例管理" key="cases">
                    <Card>
                        <Space direction="vertical" style={{ width: '100%' }}>
                            {/* 操作栏 */}
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Space>
                                    <Search
                                        placeholder="搜索PKL文件名或VIN"
                                        allowClear
                                        onSearch={handleSearch}
                                        style={{ width: 300 }}
                                    />
                                    <span>已选择 {selectedCaseIds.length} 个案例</span>
                                </Space>

                                <Space>
                                    <Upload
                                        beforeUpload={handleUploadCSV}
                                        showUploadList={false}
                                        accept=".csv"
                                    >
                                        <Button icon={<UploadOutlined />}>
                                            导入CSV
                                        </Button>
                                    </Upload>

                                    <Popconfirm
                                        title="确定要删除选中的案例吗？"
                                        onConfirm={handleDeleteSelected}
                                        disabled={selectedCaseIds.length === 0}
                                    >
                                        <Button
                                            icon={<DeleteOutlined />}
                                            danger
                                            disabled={selectedCaseIds.length === 0}
                                        >
                                            删除选中 ({selectedCaseIds.length})
                                        </Button>
                                    </Popconfirm>
                                </Space>
                            </div>

                            {/* 案例表格 */}
                            <Spin spinning={loading}>
                                <Table
                                    columns={columns}
                                    dataSource={cases}
                                    rowKey="id"
                                    rowSelection={rowSelection}
                                    pagination={{
                                        current: pagination.current,
                                        pageSize: pagination.pageSize,
                                        total: pagination.total,
                                        onChange: handlePageChange,
                                        showSizeChanger: false,
                                        showTotal: (total) => `共 ${total} 个案例`,
                                    }}
                                    scroll={{ y: 400 }}
                                    size="small"
                                />
                            </Spin>
                        </Space>
                    </Card>
                </TabPane>
            </Tabs>
        </Modal>
    );
};

export default EvaluationSetEditor;
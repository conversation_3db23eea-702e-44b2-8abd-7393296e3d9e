.resizable-sider {
    background: #fff;
    height: 100vh;
    position: relative;
    overflow: hidden;
  }
  
  .resizable-sider.left {
    border-right: 1px solid #f0f0f0;
  }
  
  .resizable-sider.right {
    border-left: 1px solid #f0f0f0;
  }
  
  .resizable-sider-content {
    height: 100%;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }
  
  /* 自定义调整手柄样式 */
  .resizable-handle {
    position: absolute;
    width: 10px;
    height: 100%;
    top: 0;
    background-color: transparent;
    cursor: col-resize;
    z-index: 10;
    transition: background-color 0.2s;
  }
  
  .resizable-handle:hover,
  .resizable-handle:active {
    background-color: rgba(24, 144, 255, 0.2);
  }
  
  .resizable-handle-right {
    right: 0;
    margin-right: -5px;
  }
  
  .resizable-handle-left {
    left: 0;
    margin-left: -5px;
  }
  
  /* 确保拖动时内容不会被选中 */
  .resizable-handle-container {
    user-select: none;
  }
/* eslint-disable prefer-const */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useRef, useState } from "react";
import * as THREE from "three";
import { OrbitControls } from "three/addons/controls/OrbitControls.js";
import axios from "axios";
import { Line2 } from "three/examples/jsm/lines/Line2.js";
import { LineMaterial } from "three/examples/jsm/lines/LineMaterial.js";
import { EvaluationCase, PdpPathInfo, DlpTrajectoryInfo } from "../types";
import { LineGeometry } from "three/examples/jsm/lines/LineGeometry.js";
// 评测集数据结构

// 定义组件的Props
interface PickleVisualizerProps {
  evaluationCase: EvaluationCase; // 可选，如果提供则使用该评测集的pkl路径
  onClose?: () => void; // 关闭可视化视图的回调函数
  height?: string | number; // 可视化区域高度
  pklId?: number; // 新增属性: 直接使用pkl ID
  highlightPathIndex?: number | null; // 新增属性: 高亮路径索引
  pdpPaths?: Record<number, PdpPathInfo>;
  dlpTrajs?: Record<number, DlpTrajectoryInfo>;
  highlightTrajIndex?: number | null;
  showGroundTruth?: boolean; // 是否有地面真实路径
  changeIds?: any;
  selectedObjectIds?: string[];
}

// 定义后端返回的数据结构类型
interface VisualizationData {
  polylines: {
    id: string;
    points: number[][];
    color: string;
    thickness: number;
    type: string;
  }[];
  polygons: {
    id: string;
    vertices: number[][];
    color: string;
    opacity: number;
    type: string;
  }[];
  arrows: {
    id: string;
    start: number[];
    end: number[];
    color: string;
    headSize: number;
  }[];
  texts: {
    id: string;
    position: number[];
    content: string;
    fontSize: number;
    color: string;
  }[];
  objects: {
    id: string;
    position: number[];
    dimensions: {
      width: number;
      height: number;
      depth: number;
    };
    rotation: number[];
    color: string;
    type: string;
    opacity: number;
  }[];
  tbt?: {
    dist: number;
    maneuver: string;
    lane_action: string;
  };
  metadata: {
    filename: string;
    timestamp: string;
    ego_heading: number;
  };
  traffic_lights?: {
    color: string;
    sign: string;
  };
  future_obj_infos?:{
    id: string;
    points: number[];
    timestamps: number[];
    rotation: number[];
  }
}

let objectList: string[] = [];

const PickleVisualizer: React.FC<PickleVisualizerProps> = (props) => {
  const {
    evaluationCase,
    height = "calc(100vh - 200px)",
    pdpPaths,
    highlightPathIndex = null,
    showGroundTruth = true,
    selectedObjectIds,
  } = props;

  const mountRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  // Three.js 对象引用
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.OrthographicCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const visualizationGroupRef = useRef<THREE.Group | null>(null);
  const inferenceGroupRef = useRef<THREE.Group | null>(null);
  const [tbtInfo, setTbtInfo] = useState<VisualizationData["tbt"] | null>(null);
  const [traffic_lights_Info, setTraffic_lights_Info] = useState<
    VisualizationData["traffic_lights"] | null
  >(null);
  const [hideOtherPaths, setHideOtherPaths] = useState<boolean>(false);
  const [selectTimestamp, setSelectTimestamp] = useState<number>(0);
  const [geolocation, setGeolocation] = useState<
    | {
        x: number | string;
        y: number | string;
      }
    | undefined
  >({
    x: 0,
    y: 0,
  });

  const handleKeyDown = (event: KeyboardEvent) => {
    // 只有在有高亮路径时才处理空格键
    if (event.code === "Space" && highlightPathIndex !== null) {
      event.preventDefault(); // 防止页面滚动
      setHideOtherPaths((prev) => !prev);
    }
  };
  useEffect(() => {
    // 添加键盘事件监听器
    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [highlightPathIndex]); // 依赖于highlightPathIndex，确保事件处理器能获取到最新的值

  useEffect(() => {
    if (selectedObjectIds) {
      objectList = selectedObjectIds;
    }
  }, [selectedObjectIds]);

  // 初始化 Three.js 场景
  useEffect(() => {
    if (!mountRef.current) return;

    // 先清除可能存在的canvas
    while (mountRef.current.firstChild) {
      mountRef.current.removeChild(mountRef.current.firstChild);
    }

    // 创建场景
    const scene = new THREE.Scene();
    // scene.position.y = -20;
    sceneRef.current = scene;
    scene.background = new THREE.Color(0x333333);
    const width = mountRef.current.clientWidth;
    const height = mountRef.current.clientHeight;

    // 创建相机
    const camera = new THREE.OrthographicCamera(75, width / height, 0.1, 1000);
    camera.position.set(0, 0, 40);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(width, height);
    renderer.setPixelRatio(window.devicePixelRatio);
    mountRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;
    // 添加轨道控制
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;

    controls.mouseButtons = {
      LEFT: THREE.MOUSE.PAN, // 左键平移
      MIDDLE: THREE.MOUSE.DOLLY, // 中键缩放
      RIGHT: THREE.MOUSE.ROTATE, // 右键旋转
    };

    // 禁用默认的旋转行为，我们将自定义Z轴旋转
    controls.enableRotate = false;
    controls.enablePan = true;
    controls.enableZoom = true;

    // 自定义Z轴旋转
    let isRotating = false;
    let lastMouseX = 0;

    const onMouseDown = (event: MouseEvent) => {
      if (event.button === 2) {
        // 右键
        isRotating = true;
        lastMouseX = event.clientX;
        event.preventDefault();
      }
    };

    const onMouseMove = (event: MouseEvent) => {
      if (isRotating) {
        const deltaX = event.clientX - lastMouseX;
        const rotationSpeed = 0.003;

        // 绕Z轴旋转场景
        if (sceneRef.current) {
          sceneRef.current.rotation.z += deltaX * rotationSpeed;
        }

        lastMouseX = event.clientX;
        event.preventDefault();
      }

      const geo: any = getGeolocation(event, renderer, camera);
      const ego = {
        x: !Number.isNaN(geo.x) ? geo.x.toFixed(2) : "0.00",
        y: !Number.isNaN(geo.y) ? geo.y.toFixed(2) : "0.00",
      };
      setGeolocation(ego);
    };

    const onMouseUp = (event: MouseEvent) => {
      if (event.button === 2) {
        // 右键
        isRotating = false;
      }
    };

    const onClick = (event: MouseEvent) => {
      const rect = renderer.domElement.getBoundingClientRect();
      const mouse = new THREE.Vector2(
        ((event.clientX - rect.left) / rect.width) * 2 - 1,
        -((event.clientY - rect.top) / rect.height) * 2 + 1
      );

      const raycaster = new THREE.Raycaster();
      raycaster.setFromCamera(mouse, camera);

      // 检测所有可交互对象（排除辅助线等）
      const interactableObjects = scene.children.filter(
        (obj) => obj.userData.isInteractable !== false
      );

      const intersects = raycaster.intersectObjects(interactableObjects, true);
      const value = intersects.filter((v) => v.object.name);
      if (value.length > 0) {
        const clickedObj: any = value[0].object;
        clickedObj.children[0].visible = !clickedObj.children[0].visible;
        const visible = clickedObj.children[0].visible;
        const defaultColor = clickedObj.defaultColor;
        // 更改物体材质颜色
        clickedObj.material.color.set(visible ? "#ff9900" : defaultColor);
        const id = String(clickedObj.name);
        const index = objectList.indexOf(id);
        if (index === -1) {
          objectList.push(id);
        } else {
          objectList.splice(index, 1);
        }
        props.changeIds(objectList);
      }
    };

    // 添加事件监听器
    renderer.domElement.addEventListener("mousedown", onMouseDown);
    renderer.domElement.addEventListener("mousemove", onMouseMove);
    renderer.domElement.addEventListener("mouseup", onMouseUp);
    renderer.domElement.addEventListener("contextmenu", (e) =>
      e.preventDefault()
    ); // 禁用右键菜单

    renderer.domElement.addEventListener("click", onClick);

    controlsRef.current = controls;

    // 创建并挂载两个组
    const vizGroup = new THREE.Group();
    vizGroup.userData = { type: "visualization_group" };
    scene.add(vizGroup);
    visualizationGroupRef.current = vizGroup;

    const infGroup = new THREE.Group();
    infGroup.userData = { type: "inference_group" };
    scene.add(infGroup);
    inferenceGroupRef.current = infGroup;

    // // 添加坐标轴
    // const axesHelper = new THREE.AxesHelper(100);
    // axesHelper.position.set(0,0,0)
    // scene.add(axesHelper);

    // const gridHelper = new THREE.GridHelper(10,10,0xff0000);
    // gridHelper.position.set(0,0,0)
    // gridHelper.rotation.set(Math.PI / 2,0,0)
    // scene.add(gridHelper);

    // 添加环境光和平行光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    // 从正上方照射下来，位置在z轴正向
    directionalLight.position.set(0, 0, 10);
    // 确保光线朝下照射
    directionalLight.target.position.set(0, 0, 0);
    scene.add(directionalLight);
    scene.add(directionalLight.target);

    // 可以添加一个较弱的环境光来防止场景中的暗部过于黑暗
    // const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
    // scene.add(ambientLight);

    // 动画循环
    const animate = () => {
      requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    };
    animate();

    // 处理窗口大小变化
    const handleResize = () => {
      if (!mountRef.current) return;

      const width = mountRef.current.clientWidth;
      const height = mountRef.current.clientHeight;
      const aspect = width / height;

      // 更新正交相机的参数
      const orthoCam = camera as THREE.OrthographicCamera;
      const frustumSize = 50;
      orthoCam.left = (-frustumSize * aspect) / 2;
      orthoCam.right = (frustumSize * aspect) / 2;
      orthoCam.top = frustumSize / 2;
      orthoCam.bottom = -frustumSize / 2;

      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    // 清理函数
    return () => {
      window.removeEventListener("resize", handleResize);
      if (rendererRef.current) {
        rendererRef.current.domElement.removeEventListener(
          "mousedown",
          onMouseDown
        );
        rendererRef.current.domElement.removeEventListener(
          "mousemove",
          onMouseMove
        );
        rendererRef.current.domElement.removeEventListener(
          "mouseup",
          onMouseUp
        );
        rendererRef.current.domElement.removeEventListener("contextmenu", (e) =>
          e.preventDefault()
        );
      }

      // 清理场景中的所有对象
      if (sceneRef.current) {
        // 递归释放场景中所有对象的资源
        const disposeObjects = (obj: THREE.Object3D) => {
          while (obj.children.length > 0) {
            disposeObjects(obj.children[0]);
            obj.remove(obj.children[0]);
          }

          if (obj instanceof THREE.Mesh) {
            if (obj.geometry) {
              obj.geometry.dispose();
            }

            if (obj.material) {
              if (Array.isArray(obj.material)) {
                obj.material.forEach((material) => material.dispose());
              } else {
                obj.material.dispose();
              }
            }
          }
        };

        disposeObjects(sceneRef.current);
      }

      // 清理渲染器
      if (rendererRef.current) {
        rendererRef.current.dispose();
        mountRef.current?.removeChild(rendererRef.current.domElement);
      }

      // 清理控制器
      if (controlsRef.current) {
        controlsRef.current.dispose();
      }
    };
  }, []);

  // 清空三维组的辅助函数
  const clearGroup = (group: THREE.Group | null) => {
    if (!group) return;
    while (group.children.length) {
      group.remove(group.children[0]);
    }
  };

  // 基础可视化渲染
  const fetchAndRenderVisualization = async (filePath?: string) => {
    setLoading(true);
    setError(null);
    try {
      const resp = await axios.post("/api/visualize-pickle", {
        pickle_path: filePath,
        config: {},
      });
      const data: VisualizationData = resp.data;
      if (data.tbt) {
        setTbtInfo(data.tbt);
      } else {
        setTbtInfo(null);
      }
      if (data.traffic_lights) {
        setTraffic_lights_Info(data.traffic_lights);
      } else {
        setTraffic_lights_Info(null);
      }
      // 渲染到 visualizationGroup
      if (visualizationGroupRef.current) {
        clearGroup(visualizationGroupRef.current);
        renderBasicData(data);
      }
      // if (pdpPaths && Object.keys(pdpPaths).length > 0) {
      renderPdpPaths();
      // }
      // currentVisualizationData.current = data;
    } catch (err) {
      setError("获取可视化数据失败，请检查连接");
      setTbtInfo(null);
      setTraffic_lights_Info(null);
    } finally {
      setLoading(false);
    }
  };

  // 单独渲染基础部分（polygons, polylines, arrows, objects）
  const renderBasicData = (data: VisualizationData) => {
    // console.log('selectedObjectIds1111', selectedObjectIds)

    const group = visualizationGroupRef.current!;
    let ego = data.metadata?.ego_heading ?? (data.objects[0]?.rotation[2] || 0);
    group.rotation.set(0, 0, -ego - 1.57);
    const parent = inferenceGroupRef.current!;
    parent.rotation.z = visualizationGroupRef.current?.rotation.z || 0;
    const hasGroundTruth =
      pdpPaths &&
      Object.values(pdpPaths).some((path) => path.is_ground_truth === true);

    // 渲染多边形
    data.polygons.forEach((polygon) => {
      const points: THREE.Vector3[] = [];
      for (const vertex of polygon.vertices) {
        points.push(new THREE.Vector3(vertex[0], vertex[1], vertex[2]));
      }

      const shape = new THREE.Shape();
      shape.moveTo(points[0].x, points[0].y);
      for (let i = 1; i < points.length; i++) {
        shape.lineTo(points[i].x, points[i].y);
      }
      shape.lineTo(points[0].x, points[0].y);

      const geometry = new THREE.ShapeGeometry(shape);
      const material = new THREE.MeshStandardMaterial({
        color: polygon.color,
        transparent: true,
        opacity: polygon.opacity,
        side: THREE.DoubleSide,
      });

      const mesh = new THREE.Mesh(geometry, material);
      mesh.userData = { type: "visualization", id: polygon.id };
      group.add(mesh);
    });

    // 渲染多段线
    data.polylines.forEach((polyline) => {
      if (!showGroundTruth && polyline.id === "ego_path") {
        return; // 如果不显示地面真实路径且当前是ego路径，则跳过渲染
      }
      if (hasGroundTruth && polyline.id === "ego_path") {
        return;
      }
      const positions: number[] = [];
      polyline.points.forEach((p) => {
        positions.push(p[0], p[1], p[2] || 0);
      });

      const geometry = new LineGeometry();
      geometry.setPositions(positions);

      const material = new LineMaterial({
        color: polyline.color,
        linewidth: polyline.thickness * 30, // 需要适当的比例转换，因为这里单位不同
        vertexColors: false,
        dashed: polyline.type === "dashed",
        resolution: new THREE.Vector2(
          mountRef.current?.clientWidth || 800,
          mountRef.current?.clientHeight || 600
        ),
      });

      const line = new Line2(geometry, material);
      line.computeLineDistances();
      line.userData = { type: "visualization", id: polyline.id };
      group.add(line);
    });

    data.arrows.forEach((arrow) => {
      const startVec = new THREE.Vector3(
        arrow.start[0],
        arrow.start[1],
        arrow.start[2]
      );
      const endVec = new THREE.Vector3(
        arrow.end[0],
        arrow.end[1],
        arrow.end[2]
      );

      const arrowLength = startVec.distanceTo(endVec);

      // 如果箭头长度过小，则不渲染，以避免计算错误
      if (arrowLength < 0.0001) {
        return;
      }

      const direction = endVec.clone().sub(startVec).normalize();

      // 定义箭头头部（锥形）的尺寸
      // coneHeight 对应 ArrowHelper 中的 headLength
      const coneHeight = arrow.headSize * arrowLength;
      // coneRadius 对应 ArrowHelper 中的 headWidth / 2
      // ArrowHelper 的 headWidth 参数是直径，ConeGeometry 需要半径
      const coneRadius = (arrow.headSize * arrowLength) / 2;

      // 如果计算出的头部尺寸过小或为零，则不渲染
      if (coneHeight <= 0.0001 || coneRadius <= 0.0001) {
        return;
      }

      // 创建锥形几何体
      const coneGeometry = new THREE.ConeGeometry(coneRadius, coneHeight, 16); // 参数: radius, height, radialSegments
      const coneMaterial = new THREE.MeshStandardMaterial({
        color: new THREE.Color(arrow.color),
      });
      const coneMesh = new THREE.Mesh(coneGeometry, coneMaterial);

      // 定位锥形
      // ConeGeometry 默认沿Y轴正方向，尖端在 Y = coneHeight / 2
      // 我们需要将锥体重心定位，使得其尖端在 arrow.end 位置
      // 锥体重心 = endVec - direction * (coneHeight / 2)
      coneMesh.position
        .copy(endVec)
        .addScaledVector(direction, -coneHeight / 2);

      // 设置锥形的方向
      // 默认锥体指向Y轴正方向 (0,1,0)
      const yAxis = new THREE.Vector3(0, 1, 0);
      coneMesh.quaternion.setFromUnitVectors(yAxis, direction);

      coneMesh.userData = {
        type: "visualization",
        id: arrow.id,
        isArrowhead: true,
      };
      group.add(coneMesh);
    });

    // 渲染3D对象
    data.objects.forEach((obj) => {
      if (Number(obj.id.split("_")[2]) === 5) {
        const checked = selectedObjectIds?.includes(String(obj.id));
        const geometry = new THREE.BoxGeometry(
          obj.dimensions.width,
          obj.dimensions.height,
          obj.dimensions.depth
        );

        // 主物体材质
        const material = new THREE.MeshStandardMaterial({
          color: checked ? "#ff9900" : obj.color,
          opacity: obj.opacity,
          transparent: true,
        });

        // 创建主物体
        const mesh: any = new THREE.Mesh(geometry, material);
        mesh.position.set(obj.position[0], obj.position[1], obj.position[2]);
        mesh.rotation.set(obj.rotation[0], obj.rotation[1], obj.rotation[2]);
        mesh.userData = { type: "visualization", id: obj.id };
        mesh.defaultColor = obj.color;
        mesh.name = obj.id;

        // 创建边框几何体
        const edges = new THREE.EdgesGeometry(geometry);
        const lineMaterial = new THREE.LineBasicMaterial({
          color: 0xff9900,
          linewidth: 2,
        });

        // 创建边框对象
        const border = new THREE.LineSegments(edges, lineMaterial);
        border.visible = checked ?? false;
        mesh.add(border);
        group.add(mesh);
      }
    });

    data.texts.forEach((text, index) => {
      if (Number(text.id.split("_")[2]) === 5) {
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        if (context) {
          const id = data.objects[index].id;
          canvas.width = 400;
          canvas.height = 200;
          context.font = `${text.fontSize}px Arial`;
          context.textBaseline = "top";
          context.fillStyle = text.color;
          // 计算行高
          const lineHeight = text.fontSize * 1.2;
          // 第一行文本
          context.fillText(id, 130, 70);
          // 第二行文本
          context.fillText(text.content, 130, 70 + lineHeight);
          const texture = new THREE.CanvasTexture(canvas);
          const spriteMaterial = new THREE.SpriteMaterial({
            map: texture,
            transparent: true,
          });

          const sprite = new THREE.Sprite(spriteMaterial);
          sprite.position.set(
            text.position[0],
            text.position[1],
            text.position[2]
          );
          sprite.scale.set(10, 5, 1);
          sprite.userData = { type: "visualization", id: id };

          group.add(sprite);
        }
      }
    });
  };

  // 仅渲染推理轨迹
  const getPathColor = (path: PdpPathInfo, isHighlighted: boolean): number => {
    if (isHighlighted) {
      return 0xffff00; // 高亮黄色
    }
    if (path.color) {
      // 如果提供了自定义颜色，将十六进制字符串转换为数字
      const colorStr = path.color.replace("#", "");
      return parseInt(colorStr, 16);
    }

    if (path.annotation?.annotation) {
      switch (path.annotation.annotation.toLowerCase()) {
        case "good":
          return 0x00ff00; // 绿色
        case "bad":
          return 0xff0000; // 红色
        case "unknown":
          return 0x0000ff; // 蓝色
        default:
          return 0x808080; // 灰色（默认）
      }
    }

    return 0x808080; // 无标注时为灰色
  };
  const getPathLineWidth = (
    path: PdpPathInfo,
    isHighlighted: boolean
  ): number => {
    // 如果高亮，使用高亮宽度
    if (isHighlighted) {
      return 4;
    }

    // 新增：使用自定义宽度，如果提供的话
    if (path.lineWidth !== undefined) {
      return path.lineWidth;
    }

    // 默认宽度
    return 1;
  };

  const renderPdpPaths = () => {
    if (
      !pdpPaths ||
      Object.keys(pdpPaths).length === 0 ||
      !inferenceGroupRef.current
    ) {
      console.log("No PDP paths to render");
      return;
    }
    clearGroup(inferenceGroupRef.current);
    Object.values(pdpPaths).forEach((path) => {
      if (!path.visualization_points || path.visualization_points.length < 2)
        return;

      const points = path.visualization_points;
      const positions: number[] = [];

      // 将点坐标展平为一维数组
      points.forEach((point) => {
        positions.push(point[0], point[1], point[2] || 0.5); // 如果没有z坐标,默认为0.5
      });

      const geometry = new LineGeometry();
      geometry.setPositions(positions);

      const isHighlighted = path.index === highlightPathIndex;
      const color = getPathColor(path, isHighlighted);
      // const lineWidth = isHighlighted ? 4 : 2;
      const lineWidth = getPathLineWidth(path, isHighlighted);

      const material = new LineMaterial({
        color: color,
        linewidth: lineWidth,
        resolution: new THREE.Vector2(
          mountRef.current?.clientWidth || 800,
          mountRef.current?.clientHeight || 600
        ),
      });

      const line = new Line2(geometry, material);
      line.computeLineDistances();
      line.userData = {
        type: "pdp_path",
        id: `path_${path.index}`,
        probability: path.probability,
      };

      inferenceGroupRef.current!.add(line);

      const middle_point = path.middle_point || [0, 0, 0];
      const sphereGeometry = new THREE.SphereGeometry(0.4, 16, 16);
      const sphereMaterial = new THREE.MeshStandardMaterial({
        color: 0xff0000,
      });
      const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
      sphere.position.set(
        middle_point[0],
        middle_point[1],
        middle_point[2] || 0.5
      );
      sphere.userData = {
        type: "pdp_path",
        id: `path_${path.index}_sphere`,
      };
      inferenceGroupRef.current!.add(sphere);
    });
  };

  // // 添加到现有 useEffect 中,在评测数据加载后渲染PDP路径
  useEffect(() => {
    // 如果有 PDP 路径数据,渲染这些路径
    if (pdpPaths && Object.keys(pdpPaths).length > 0) {
      renderPdpPaths();
    }
  }, [pdpPaths]);

  // 监听高亮路径索引变化
  useEffect(() => {
    if (!pdpPaths || !inferenceGroupRef.current) return;
    // renderPdpPaths();

    inferenceGroupRef.current.children.forEach((child) => {
      if (child.userData.type === "pdp_path") {
        const pathIndex = parseInt(child.userData.id.split("_")[1]);
        const path = pdpPaths[pathIndex];
        if (!path) return;

        const isHighlighted = pathIndex === highlightPathIndex;

        // 新增：根据hideOtherPaths状态控制可见性
        if (hideOtherPaths && highlightPathIndex !== null) {
          child.visible = isHighlighted;
        } else {
          child.visible = true;
        }

        // 如果路径可见，更新其样式
        if (child.visible) {
          const material = (child as Line2).material as LineMaterial;
          const color = getPathColor(path, isHighlighted);
          const lineWidth = isHighlighted ? 2 : 1;

          material.color.set(color);
          material.linewidth = lineWidth;
        }
      }
    });
  }, [highlightPathIndex, hideOtherPaths]); // 新增hideOtherPaths依赖

  // evaluationCase 变更：全量重绘基础，可复用或获取轨迹
  useEffect(() => {
    if (!evaluationCase?.pkl_dir || !evaluationCase.pkl_name) return;
    const fullPath = `${evaluationCase.pkl_dir}/${evaluationCase.pkl_name}`;
    // 全量清空
    clearGroup(visualizationGroupRef.current);
    clearGroup(inferenceGroupRef.current);
    // 重绘基础
    fetchAndRenderVisualization(fullPath);
    // console.log('fetchAndRenderVisualization');
  }, [evaluationCase]);

  // 获得坐标位置
  const getGeolocation = (
    e: MouseEvent,
    renderer: THREE.WebGLRenderer,
    camera: THREE.OrthographicCamera
  ) => {
    if (!renderer?.domElement) return;
    const canvas = renderer.domElement;
    const rect = canvas.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / canvas.clientWidth) * 2 - 1;
    const y = -((e.clientY - rect.top) / canvas.clientHeight) * 2 + 1;
    const worldPos = new THREE.Vector3(x, y, 0).unproject(camera);
    return new THREE.Vector3(worldPos.x, worldPos.y, 0);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="bg-gray-100 p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          {tbtInfo && (
            <div
              style={{
                background: "white",
                padding: "10px",
                borderRadius: "0.5rem",
                boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  gap: "1.5rem",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    background: "#eeeeee",
                    padding: "0.5rem 1rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  <span style={{ fontWeight: "bold" }}>
                    x:{geolocation?.x} y:{geolocation?.y}
                  </span>
                </div>

                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    background: "#e6f7ff",
                    padding: "0.5rem 1rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {/* <span style={{ fontWeight: 500, marginRight: '0.5rem' }}>距离:</span> */}
                  <span style={{ fontWeight: "bold" }}>
                    {tbtInfo.dist.toFixed(1)}米
                  </span>
                </div>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    background: "#f6ffed",
                    padding: "0.5rem 1rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {/* <span style={{ fontWeight: 500, marginRight: '0.5rem' }}>操作:</span> */}
                  <span style={{ fontWeight: "bold" }}>{tbtInfo.maneuver}</span>
                </div>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    background: "#f9f0ff",
                    padding: "0.5rem 1rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {/* <span style={{ fontWeight: 500, marginRight: '0.5rem' }}>推荐车道:</span> */}
                  <span style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                    {tbtInfo.lane_action}
                  </span>
                </div>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    background: "#f9f0ff",
                    padding: "0.5rem 1rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {/* <span style={{ fontWeight: 500, marginRight: '0.5rem' }}>推荐车道:</span> */}
                  <span
                    style={{
                      fontWeight: "bold",
                      whiteSpace: "nowrap",
                      color: traffic_lights_Info?.color,
                    }}
                  >
                    {traffic_lights_Info?.sign}
                  </span>
                </div>
                {/* 添加时间戳输入框 */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    background: "#fffbe6",
                    padding: "0.5rem 1rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  <label style={{ fontWeight: "bold", marginRight: "0.5rem" }}>
                    时间戳:
                  </label>
                  <input
                    type="number"
                    step="0.2"
                    min="0"
                    max="4"
                    value={selectTimestamp == 0 ? "" : selectTimestamp}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value);
                      if (!isNaN(value) && value >= 0 && value <= 4) {
                        setSelectTimestamp(value);
                      } else if (isNaN(value)) {
                        setSelectTimestamp(0);
                      }
                    }}
                    style={{
                      width: "60px",
                      padding: "0.25rem",
                      border: "1px solid #d9d9d9",
                      borderRadius: "4px",
                      textAlign: "center",
                    }}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
        {error && <p className="text-red-500 mt-2">{error}</p>}
        {loading && <p className="text-blue-500 mt-2">正在加载可视化数据...</p>}
      </div>

      <div ref={mountRef} className="flex-grow" style={{ height }}></div>
    </div>
  );
};

export default PickleVisualizer;

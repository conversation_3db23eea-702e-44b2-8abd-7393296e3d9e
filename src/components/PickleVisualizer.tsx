import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import axios from 'axios';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { EvaluationCase, PdpPathInfo } from '../types';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
// 评测集数据结构

// 定义组件的Props
interface PickleVisualizerProps {
    evaluationCase: EvaluationCase;  // 可选，如果提供则使用该评测集的pkl路径
    onClose?: () => void;  // 关闭可视化视图的回调函数
    height?: string | number; // 可视化区域高度
    pklId?: number;  // 新增属性: 直接使用pkl ID
    highlightPathIndex?: number | null;  // 新增属性: 高亮路径索引
    pdpPaths?: Record<number, PdpPathInfo>;
    showGroundTruth?: boolean; // 是否有地面真实路径
}

// 定义后端返回的数据结构类型
interface VisualizationData {
    polylines: {
        id: string;
        points: number[][];
        color: string;
        thickness: number;
        type: string;
    }[];
    polygons: {
        id: string;
        vertices: number[][];
        color: string;
        opacity: number;
        type: string;
    }[];
    arrows: {
        id: string;
        start: number[];
        end: number[];
        color: string;
        headSize: number;
    }[];
    texts: {
        id: string;
        position: number[];
        content: string;
        fontSize: number;
        color: string;
    }[];
    objects: {
        id: string;
        position: number[];
        dimensions: {
            width: number;
            height: number;
            depth: number;
        };
        rotation: number[];
        color: string;
        type: string;
        opacity: number;
    }[];
    tbt?: {
        dist: number;
        maneuver: string;
        lane_action: string;
    };
    metadata: {
        filename: string;
        timestamp: string;
        ego_heading: number;
    };
}

const PickleVisualizer: React.FC<PickleVisualizerProps> = ({
    evaluationCase,
    height = 'calc(100vh - 120px)',
    pdpPaths,
    highlightPathIndex = null,
    showGroundTruth = true
}) => {
    const mountRef = useRef<HTMLDivElement>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    // Three.js 对象引用
    const sceneRef = useRef<THREE.Scene | null>(null);
    const cameraRef = useRef<THREE.OrthographicCamera | null>(null);
    const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
    const controlsRef = useRef<OrbitControls | null>(null);
    const visualizationGroupRef = useRef<THREE.Group | null>(null);
    const inferenceGroupRef = useRef<THREE.Group | null>(null);
    const [tbtInfo, setTbtInfo] = useState<VisualizationData['tbt'] | null>(null);
    const [hideOtherPaths, setHideOtherPaths] = useState<boolean>(false);
    
    // 新增：用于跟踪object的原始颜色和当前状态
    const originalColors = useRef<Map<string, THREE.Color>>(new Map());
    const [selectedObjects, setSelectedObjects] = useState<Set<string>>(new Set());

    const handleKeyDown = (event: KeyboardEvent) => {
        // 只有在有高亮路径时才处理空格键
        if (event.code === 'Space' && highlightPathIndex !== null) {
            event.preventDefault(); // 防止页面滚动
            setHideOtherPaths(prev => !prev);
        }
    };

    // 新增：处理鼠标点击交互
    const handleMouseClick = (event: MouseEvent) => {
        if (!sceneRef.current || !cameraRef.current || !mountRef.current) return;

        // 防止在拖拽或旋转时触发点击
        if (event.defaultPrevented) return;

        // 计算鼠标在标准化设备坐标系中的位置 (-1 到 +1)
        const rect = mountRef.current.getBoundingClientRect();
        const mouse = new THREE.Vector2();
        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        // 创建射线投射器
        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, cameraRef.current);

        // 获取所有可交互的对象（仅包括3D objects，不包括线条和其他元素）
        const interactableObjects: THREE.Object3D[] = [];
        
        if (visualizationGroupRef.current) {
            visualizationGroupRef.current.traverse((child) => {
                if (child instanceof THREE.Mesh && 
                    child.userData.type === 'visualization' && 
                    child.geometry instanceof THREE.BoxGeometry) {
                    interactableObjects.push(child);
                }
            });
        }

        // 检测射线与对象的交点
        const intersects = raycaster.intersectObjects(interactableObjects);
        
        if (intersects.length > 0) {
            const clickedObject = intersects[0].object as THREE.Mesh;
            const objectId = clickedObject.userData.id;
            
            if (clickedObject.material instanceof THREE.MeshStandardMaterial) {
                // 仅更新React状态，不直接修改Three.js对象
                setSelectedObjects(prevSelectedObjects => {
                    const newSelectedObjects = new Set(prevSelectedObjects);

                    if (newSelectedObjects.has(objectId)) {
                        // 取消选中
                        newSelectedObjects.delete(objectId);
                    } else {
                        // 选中
                        newSelectedObjects.add(objectId);
                        // 如果是第一次选中，保存其原始颜色
                        if (!originalColors.current.has(objectId)) {
                            const material = clickedObject.material as THREE.MeshStandardMaterial;
                            originalColors.current.set(objectId, material.color.clone());
                        }
                    }
                    console.log('%c[Click Handler] State update requested. New selection:', 'color: orange; font-weight: bold;', newSelectedObjects);

                    return newSelectedObjects;
                });
            }
        }
    };

    // 新增：此useEffect负责根据React状态同步Three.js场景的颜色
    useEffect(() => {
        if (!visualizationGroupRef.current) return;
        console.log('%c[Effect: selectedObjects] Fired. Current selection:', 'color: blue; font-weight: bold;', selectedObjects);

        visualizationGroupRef.current.traverse((child) => {
            // 确保是可交互的Mesh对象
            if (child instanceof THREE.Mesh && child.userData.id && child.userData.type === 'visualization') {
                const objectId = child.userData.id;
                const material = child.material as THREE.MeshStandardMaterial;
                material.needsUpdate = true;

                if (selectedObjects.has(objectId)) {
                    // 应用高亮颜色
                    console.log(`%c  -> Highlighting object ${objectId} to yellow.`, 'color: green');

                    material.color.set(0xffff00); 
                } else {
                    // 恢复原始颜色
                    const originalColor = originalColors.current.get(objectId);
                    if (originalColor) {
                        console.log(`  -> Restoring original color for object ${objectId}.`);

                        material.color.copy(originalColor);
                    }
                }
                // 标记材质需要更新以在下一帧生效
            }
        });
    }, [selectedObjects]); // 依赖项是selectedObjects，每当选择集变化时执行

    useEffect(() => {
        // 添加键盘事件监听器
        window.addEventListener('keydown', handleKeyDown);

        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [highlightPathIndex]); // 依赖于highlightPathIndex，确保事件处理器能获取到最新的值
    
    // 初始化 Three.js 场景
    useEffect(() => {
        if (!mountRef.current) return;
        // 先清除可能存在的canvas
        while (mountRef.current.firstChild) {
            mountRef.current.removeChild(mountRef.current.firstChild);
        }

        // 创建场景
        const scene = new THREE.Scene();
        scene.position.y = -20;
        sceneRef.current = scene;
        scene.background = new THREE.Color(0x333333);
        const width = mountRef.current.clientWidth;
        const height = mountRef.current.clientHeight;

        // 修复相机创建 - 使用正确的正交相机参数
        const aspect = width / height;
        const frustumSize = 100; // 调整视野大小
        const camera = new THREE.OrthographicCamera(
            -frustumSize * aspect / 2, // left
            frustumSize * aspect / 2,  // right
            frustumSize / 2,           // top
            -frustumSize / 2,          // bottom
            0.1,                       // near
            1000                       // far
        );
        camera.position.set(0, 0, 40);
        camera.lookAt(0, 0, 0);
        cameraRef.current = camera;

        // 创建渲染器
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(width, height);
        renderer.setPixelRatio(window.devicePixelRatio);
        mountRef.current.appendChild(renderer.domElement);
        rendererRef.current = renderer;
        // 添加轨道控制
        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;

        controls.mouseButtons = {
            LEFT: THREE.MOUSE.PAN,      // 左键平移
            MIDDLE: THREE.MOUSE.DOLLY,  // 中键缩放
            RIGHT: THREE.MOUSE.ROTATE   // 右键旋转
        };

        // 禁用默认的旋转行为，我们将自定义Z轴旋转
        controls.enableRotate = false;
        controls.enablePan = true;
        controls.enableZoom = true;

        // 自定义Z轴旋转
        let isRotating = false;
        let isPanning = false;
        let lastMouseX = 0;
        let mouseDownTime = 0;
        let mouseDownPosition = { x: 0, y: 0 };

        const onMouseDown = (event: MouseEvent) => {
            mouseDownTime = Date.now();
            mouseDownPosition = { x: event.clientX, y: event.clientY };
            
            if (event.button === 0) { // 左键
                isPanning = true;
            } else if (event.button === 2) { // 右键
                isRotating = true;
                lastMouseX = event.clientX;
                event.preventDefault();
            }
        };

        const onMouseMove = (event: MouseEvent) => {
            if (isRotating) {
                const deltaX = event.clientX - lastMouseX;
                const rotationSpeed = 0.003;

                // 绕Z轴旋转场景
                if (sceneRef.current) {
                    sceneRef.current.rotation.z += deltaX * rotationSpeed;
                }

                lastMouseX = event.clientX;
                event.preventDefault();
            }
        };

        const onMouseUp = (event: MouseEvent) => {
            if (event.button === 2) { // 右键
                isRotating = false;
            } else if (event.button === 0) { // 左键
                isPanning = false;
            }
        };

        // 修改点击事件处理，避免与拖拽冲突
        const onMouseClick = (event: MouseEvent) => {
            // 计算鼠标移动距离和点击持续时间
            const timeDiff = Date.now() - mouseDownTime;
            const distance = Math.sqrt(
                Math.pow(event.clientX - mouseDownPosition.x, 2) + 
                Math.pow(event.clientY - mouseDownPosition.y, 2)
            );
            
            // 只有在短时间内且鼠标移动距离很小时才认为是点击
            if (timeDiff < 300 && distance < 10) { // 增加了容忍距离
                handleMouseClick(event);
            }
        };

        // 添加事件监听器
        renderer.domElement.addEventListener('mousedown', onMouseDown);
        renderer.domElement.addEventListener('mousemove', onMouseMove);
        renderer.domElement.addEventListener('mouseup', onMouseUp);
        renderer.domElement.addEventListener('contextmenu', (e) => e.preventDefault()); // 禁用右键菜单
        
        // 新增：添加鼠标点击事件监听器
        renderer.domElement.addEventListener('click', onMouseClick);

        controlsRef.current = controls;

        // 创建并挂载两个组
        const vizGroup = new THREE.Group();
        vizGroup.userData = { type: 'visualization_group' };
        scene.add(vizGroup);
        visualizationGroupRef.current = vizGroup;

        const infGroup = new THREE.Group();
        infGroup.userData = { type: 'inference_group' };
        scene.add(infGroup);
        inferenceGroupRef.current = infGroup;

        // 添加坐标轴
        const axesHelper = new THREE.AxesHelper(5);
        scene.add(axesHelper);

        // 添加环境光和平行光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        // 从正上方照射下来，位置在z轴正向
        directionalLight.position.set(0, 0, 10);
        // 确保光线朝下照射
        directionalLight.target.position.set(0, 0, 0);
        scene.add(directionalLight);
        scene.add(directionalLight.target);

        // 可以添加一个较弱的环境光来防止场景中的暗部过于黑暗
        // const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        // scene.add(ambientLight);

        // 动画循环
        const animate = () => {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        };
        animate();

        // 处理窗口大小变化
        const handleResize = () => {
            if (!mountRef.current) return;

            const width = mountRef.current.clientWidth;
            const height = mountRef.current.clientHeight;
            const aspect = width / height;

            // 更新正交相机的参数
            const orthoCam = camera as THREE.OrthographicCamera;
            const frustumSize = 50;
            orthoCam.left = -frustumSize * aspect / 2;
            orthoCam.right = frustumSize * aspect / 2;
            orthoCam.top = frustumSize / 2;
            orthoCam.bottom = -frustumSize / 2;

            camera.updateProjectionMatrix();
            renderer.setSize(width, height);
        };

        window.addEventListener('resize', handleResize);
        handleResize();

        // 清理函数
        return () => {
            window.removeEventListener('resize', handleResize);
            if (rendererRef.current) {
                rendererRef.current.domElement.removeEventListener('mousedown', onMouseDown);
                rendererRef.current.domElement.removeEventListener('mousemove', onMouseMove);
                rendererRef.current.domElement.removeEventListener('mouseup', onMouseUp);
                rendererRef.current.domElement.removeEventListener('contextmenu', (e) => e.preventDefault());
                // 新增：移除点击事件监听器
                rendererRef.current.domElement.removeEventListener('click', onMouseClick);
            }

            // 清理场景中的所有对象
            if (sceneRef.current) {
                // 递归释放场景中所有对象的资源
                const disposeObjects = (obj: THREE.Object3D) => {
                    while (obj.children.length > 0) {
                        disposeObjects(obj.children[0]);
                        obj.remove(obj.children[0]);
                    }

                    if (obj instanceof THREE.Mesh) {
                        if (obj.geometry) {
                            obj.geometry.dispose();
                        }

                        if (obj.material) {
                            if (Array.isArray(obj.material)) {
                                obj.material.forEach(material => material.dispose());
                            } else {
                                obj.material.dispose();
                            }
                        }
                    }
                };

                disposeObjects(sceneRef.current);
            }

            // 清理渲染器
            if (rendererRef.current) {
                rendererRef.current.dispose();
                mountRef.current?.removeChild(rendererRef.current.domElement);
            }

            // 清理控制器
            if (controlsRef.current) {
                controlsRef.current.dispose();
            }
            
            // 新增：清理颜色缓存
            originalColors.current.clear();
        };
    }, []);

    // 清空三维组的辅助函数
    const clearGroup = (group: THREE.Group | null) => {
        if (!group) return;
        while (group.children.length) {
            group.remove(group.children[0]);
        }
    };

    // 基础可视化渲染
    const fetchAndRenderVisualization = async (filePath?: string) => {
        setLoading(true);
        setError(null);
        
        // 新增：清理之前的状态
        originalColors.current.clear();
        setSelectedObjects(new Set());
        
        try {
            const resp = await axios.post('/api/visualize-pickle', { pickle_path: filePath, config: {} });
            const data: VisualizationData = resp.data;
            if (data.tbt) {
                setTbtInfo(data.tbt);
            } else {
                setTbtInfo(null);
            }
            // 渲染到 visualizationGroup
            if (visualizationGroupRef.current) {
                clearGroup(visualizationGroupRef.current);
                renderBasicData(data);
            }
            // if (pdpPaths && Object.keys(pdpPaths).length > 0) {
            renderPdpPaths();
            // }
            // currentVisualizationData.current = data;
        } catch (err) {
            setError('获取可视化数据失败，请检查连接');
            setTbtInfo(null);
        } finally {
            setLoading(false);
        }
    };

    // 单独渲染基础部分（polygons, polylines, arrows, objects）
    const renderBasicData = (data: VisualizationData) => {
        const group = visualizationGroupRef.current!;
        let ego = data.metadata?.ego_heading ?? (data.objects[0]?.rotation[2] || 0);
        group.rotation.set(0, 0, -ego - 1.57);
        const parent = inferenceGroupRef.current!;
        parent.rotation.z = visualizationGroupRef.current?.rotation.z || 0;
        const hasGroundTruth = pdpPaths && Object.values(pdpPaths).some(path => path.is_ground_truth === true);

        // 渲染多边形
        data.polygons.forEach(polygon => {
            const points: THREE.Vector3[] = [];
            for (const vertex of polygon.vertices) {
                points.push(new THREE.Vector3(vertex[0], vertex[1], vertex[2]));
            }

            const shape = new THREE.Shape();
            shape.moveTo(points[0].x, points[0].y);
            for (let i = 1; i < points.length; i++) {
                shape.lineTo(points[i].x, points[i].y);
            }
            shape.lineTo(points[0].x, points[0].y);

            const geometry = new THREE.ShapeGeometry(shape);
            const material = new THREE.MeshStandardMaterial({
                color: polygon.color,
                transparent: true,
                opacity: polygon.opacity,
                side: THREE.DoubleSide
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.userData = { type: 'visualization', id: polygon.id };
            group.add(mesh);
        });

        // 渲染多段线
        data.polylines.forEach(polyline => {
            if (!showGroundTruth && polyline.id === 'ego_path') {
                return; // 如果不显示地面真实路径且当前是ego路径，则跳过渲染
            }
            if (hasGroundTruth && polyline.id === 'ego_path') {
                return;
            }
            const positions: number[] = [];
            polyline.points.forEach(p => {
                positions.push(p[0], p[1], p[2] || 0);
            });

            const geometry = new LineGeometry();
            geometry.setPositions(positions);

            const material = new LineMaterial({
                color: polyline.color,
                linewidth: polyline.thickness * 30, // 需要适当的比例转换，因为这里单位不同
                vertexColors: false,
                dashed: polyline.type === 'dashed',
                resolution: new THREE.Vector2(mountRef.current?.clientWidth || 800, mountRef.current?.clientHeight || 600)
            });

            const line = new Line2(geometry, material);
            line.computeLineDistances();
            line.userData = { type: 'visualization', id: polyline.id };
            group.add(line);
        });

        data.arrows.forEach(arrow => {

            const startVec = new THREE.Vector3(arrow.start[0], arrow.start[1], arrow.start[2]);
            const endVec = new THREE.Vector3(arrow.end[0], arrow.end[1], arrow.end[2]);

            const arrowLength = startVec.distanceTo(endVec);

            // 如果箭头长度过小，则不渲染，以避免计算错误
            if (arrowLength < 0.0001) {
                return;
            }

            const direction = endVec.clone().sub(startVec).normalize();

            // 定义箭头头部（锥形）的尺寸
            // coneHeight 对应 ArrowHelper 中的 headLength
            const coneHeight = arrow.headSize * arrowLength;
            // coneRadius 对应 ArrowHelper 中的 headWidth / 2
            // ArrowHelper 的 headWidth 参数是直径，ConeGeometry 需要半径
            const coneRadius = (arrow.headSize * arrowLength) / 2;

            // 如果计算出的头部尺寸过小或为零，则不渲染
            if (coneHeight <= 0.0001 || coneRadius <= 0.0001) {
                return;
            }

            // 创建锥形几何体
            const coneGeometry = new THREE.ConeGeometry(coneRadius, coneHeight, 16); // 参数: radius, height, radialSegments
            const coneMaterial = new THREE.MeshStandardMaterial({
                color: new THREE.Color(arrow.color)
            });
            const coneMesh = new THREE.Mesh(coneGeometry, coneMaterial);

            // 定位锥形
            // ConeGeometry 默认沿Y轴正方向，尖端在 Y = coneHeight / 2
            // 我们需要将锥体重心定位，使得其尖端在 arrow.end 位置
            // 锥体重心 = endVec - direction * (coneHeight / 2)
            coneMesh.position.copy(endVec).addScaledVector(direction, -coneHeight / 2);

            // 设置锥形的方向
            // 默认锥体指向Y轴正方向 (0,1,0)
            const yAxis = new THREE.Vector3(0, 1, 0);
            coneMesh.quaternion.setFromUnitVectors(yAxis, direction);

            coneMesh.userData = { type: 'visualization', id: arrow.id, isArrowhead: true };
            group.add(coneMesh);
        });

        // 渲染3D对象
        data.objects.forEach(obj => {
            const geometry = new THREE.BoxGeometry(
                obj.dimensions.width,
                obj.dimensions.height,
                obj.dimensions.depth
            );
            // 为每个对象创建独立的材质实例，避免材质共享问题
            const material = new THREE.MeshStandardMaterial({
                color: obj.color, // 使用 THREE.Color 对象
                opacity: obj.opacity,
                transparent: true,
            });
            const mesh = new THREE.Mesh(geometry, material);

            mesh.position.set(obj.position[0], obj.position[1], obj.position[2]);
            mesh.rotation.set(obj.rotation[0], obj.rotation[1], obj.rotation[2]);
            mesh.userData = { type: 'visualization', id: obj.id };

            group.add(mesh);
        });

        data.texts.forEach(text => {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            if (context) {
                canvas.width = 200;
                canvas.height = 100;
                context.font = `${text.fontSize}px Arial`;
                context.fillStyle = text.color;
                context.fillText(text.content, 60, 30);

                const texture = new THREE.CanvasTexture(canvas);
                const spriteMaterial = new THREE.SpriteMaterial({
                    map: texture,
                    transparent: true
                });

                const sprite = new THREE.Sprite(spriteMaterial);
                sprite.position.set(text.position[0], text.position[1], text.position[2]);
                sprite.scale.set(5, 2.5, 1);
                sprite.userData = { type: 'visualization', id: text.id };

                group.add(sprite);
            }
        }
        );
    };

    // 仅渲染推理轨迹
    const getPathColor = (path: PdpPathInfo, isHighlighted: boolean): number => {
        if (isHighlighted) {
            return 0xffff00; // 高亮黄色
        }
        if (path.color) {
            // 如果提供了自定义颜色，将十六进制字符串转换为数字
            const colorStr = path.color.replace('#', '');
            return parseInt(colorStr, 16);
        }

        if (path.annotation?.annotation) {
            switch (path.annotation.annotation.toLowerCase()) {
                case 'good':
                    return 0x00ff00; // 绿色
                case 'bad':
                    return 0xff0000; // 红色
                case 'unknown':
                    return 0x0000ff; // 蓝色
                default:
                    return 0x808080; // 灰色（默认）
            }
        }

        return 0x808080; // 无标注时为灰色
    };
    const getPathLineWidth = (path: PdpPathInfo, isHighlighted: boolean): number => {
        // 如果高亮，使用高亮宽度
        if (isHighlighted) {
            return 4;
        }

        // 新增：使用自定义宽度，如果提供的话
        if (path.lineWidth !== undefined) {
            return path.lineWidth;
        }

        // 默认宽度
        return 1;
    };

    const renderPdpPaths = () => {
        if (!pdpPaths || Object.keys(pdpPaths).length === 0 || !inferenceGroupRef.current) {
            console.log('No PDP paths to render');
            return;
        }

        clearGroup(inferenceGroupRef.current);
        Object.values(pdpPaths).forEach((path) => {

            if (!path.visualization_points || path.visualization_points.length < 2) return;

            const points = path.visualization_points;
            const positions: number[] = [];

            // 将点坐标展平为一维数组
            points.forEach(point => {
                positions.push(point[0], point[1], point[2] || 0.5); // 如果没有z坐标,默认为0.5
            });

            const geometry = new LineGeometry();
            geometry.setPositions(positions);

            const isHighlighted = path.index === highlightPathIndex;
            const color = getPathColor(path, isHighlighted);
            // const lineWidth = isHighlighted ? 4 : 2;
            const lineWidth = getPathLineWidth(path, isHighlighted);

            const material = new LineMaterial({
                color: color,
                linewidth: lineWidth,
                resolution: new THREE.Vector2(mountRef.current?.clientWidth || 800, mountRef.current?.clientHeight || 600)
            });

            const line = new Line2(geometry, material);
            line.computeLineDistances();
            line.userData = {
                type: 'pdp_path',
                id: `path_${path.index}`,
                probability: path.probability
            };

            inferenceGroupRef.current!.add(line);


            const middle_point = path.middle_point || [0, 0, 0];
            const sphereGeometry = new THREE.SphereGeometry(0.4, 16, 16);
            const sphereMaterial = new THREE.MeshStandardMaterial({ color: 0xff0000 });
            const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
            sphere.position.set(middle_point[0], middle_point[1], middle_point[2] || 0.5);
            sphere.userData = {
                type: 'pdp_path',
                id: `path_${path.index}_sphere`
            };
            inferenceGroupRef.current!.add(sphere);

        });
    };

    // 移除专门处理颜色更新的useEffect，因为逻辑已移至handleMouseClick
    // 这避免了React渲染周期带来的时序问题

    // // 添加到现有 useEffect 中,在评测数据加载后渲染PDP路径
    useEffect(() => {

        // 如果有 PDP 路径数据,渲染这些路径
        if (pdpPaths && Object.keys(pdpPaths).length > 0) {
            renderPdpPaths();
        }

    }, [pdpPaths]);

    // 监听高亮路径索引变化
    useEffect(() => {
        if (!pdpPaths || !inferenceGroupRef.current) return;
        // renderPdpPaths();

        inferenceGroupRef.current.children.forEach(child => {
            if (child.userData.type === 'pdp_path') {
                const pathIndex = parseInt(child.userData.id.split('_')[1]);
                const path = pdpPaths[pathIndex];
                if (!path) return;

                const isHighlighted = pathIndex === highlightPathIndex;

                // 新增：根据hideOtherPaths状态控制可见性
                if (hideOtherPaths && highlightPathIndex !== null) {
                    child.visible = isHighlighted;
                } else {
                    child.visible = true;
                }

                // 如果路径可见，更新其样式
                if (child.visible) {
                    const material = (child as Line2).material as LineMaterial;
                    const color = getPathColor(path, isHighlighted);
                    const lineWidth = isHighlighted ? 2 : 1;

                    material.color.set(color);
                    material.linewidth = lineWidth;
                }
            }
        });
    }, [highlightPathIndex, hideOtherPaths]); // 新增hideOtherPaths依赖

    // evaluationCase 变更：全量重绘基础，可复用或获取轨迹
    useEffect(() => {

        if (!evaluationCase?.pkl_dir || !evaluationCase.pkl_name) return;
        const fullPath = `${evaluationCase.pkl_dir}/${evaluationCase.pkl_name}`;
        // 全量清空
        clearGroup(visualizationGroupRef.current);
        clearGroup(inferenceGroupRef.current);
        // 重绘基础
        fetchAndRenderVisualization(fullPath)
        // console.log('fetchAndRenderVisualization');
    }, [evaluationCase]);



    return (
        <div className="flex flex-col h-full">
            <div className="bg-gray-100 p-4 border-b">
                <div className="flex items-center justify-between mb-3">
                    {tbtInfo && (
                        <div style={{
                            background: 'white',
                            padding: '1rem',
                            borderRadius: '0.5rem',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                            width: '100%'
                        }}>
                            <div style={{
                                display: 'flex',
                                justifyContent: 'center',
                                gap: '1.5rem'
                            }}>
                                <div style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    background: '#e6f7ff',
                                    padding: '0.5rem 1rem',
                                    borderRadius: '0.5rem',
                                    boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)'
                                }}>
                                    {/* <span style={{ fontWeight: 500, marginRight: '0.5rem' }}>距离:</span> */}
                                    <span style={{ fontWeight: 'bold' }}>{tbtInfo.dist.toFixed(1)}米</span>
                                </div>
                                <div style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    background: '#f6ffed',
                                    padding: '0.5rem 1rem',
                                    borderRadius: '0.5rem',
                                    boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)'
                                }}>
                                    {/* <span style={{ fontWeight: 500, marginRight: '0.5rem' }}>操作:</span> */}
                                    <span style={{ fontWeight: 'bold' }}>{tbtInfo.maneuver}</span>
                                </div>
                                <div style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    background: '#f9f0ff',
                                    padding: '0.5rem 1rem',
                                    borderRadius: '0.5rem',
                                    boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)'
                                }}>
                                    {/* <span style={{ fontWeight: 500, marginRight: '0.5rem' }}>推荐车道:</span> */}
                                    <span style={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{tbtInfo.lane_action}</span>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
                {error && <p className="text-red-500 mt-2">{error}</p>}
                {loading && <p className="text-blue-500 mt-2">正在加载可视化数据...</p>}
                
                {/* 新增：显示交互提示 */}
                {selectedObjects.size > 0 && (
                    <div className="mt-2 p-2 bg-blue-100 border border-blue-300 rounded">
                        <p className="text-blue-700 text-sm">
                            已选中 {selectedObjects.size} 个对象。点击对象可切换选中状态。
                        </p>
                    </div>
                )}
            </div>

            <div ref={mountRef} className="flex-grow" style={{ height }}></div>
        </div>
    );
};

export default PickleVisualizer;
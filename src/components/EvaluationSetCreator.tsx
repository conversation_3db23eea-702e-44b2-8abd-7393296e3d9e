import React, { useState } from 'react';
import { Card, Form, Input, Button, List, message, Empty, Select, Upload, Tabs } from 'antd';
import { CloseOutlined, InboxOutlined } from '@ant-design/icons';
import axios from 'axios';
import './EvaluationSetCreator.css';
import { EvaluationCase } from '../types/index';

const { TabPane } = Tabs;
const { Dragger } = Upload;

interface EvaluationSetCreatorProps {
    onClose: () => void;
    onSuccess?: () => void;
}

// 定义场景标签选项
const SCENE_TAG_OPTIONS = [
    { label: '效率变道', value: 'EFFICIENCY_LCR' },
    { label: '左转', value: 'LEFT_TURN' },
    { label: '右转', value: 'RIGHT_TURN' },
    { label: '避静态障碍物', value: 'NUDGE_STATIC_OBS' },
    { label: '避慢速障碍物', value: 'NUDGE_SLOW_OBS' },
    { label: '环境约束', value: 'ENV_CONSTRAINT' },
];

const EvaluationSetCreator: React.FC<EvaluationSetCreatorProps> = ({ onClose, onSuccess }) => {
    const [form] = Form.useForm();
    const [selectedItems, setSelectedItems] = useState<EvaluationCase[]>([]);
    const [loading, setLoading] = useState(false);
    const [uploadLoading, setUploadLoading] = useState(false);


    // 处理拖放进入
    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.currentTarget.classList.add('drag-over');
    };

    // 处理拖放离开
    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.currentTarget.classList.remove('drag-over');
    };

    // 处理拖放
    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.currentTarget.classList.remove('drag-over');

        try {
            const droppedItem = JSON.parse(e.dataTransfer.getData('application/json'));

            // 检查是否已存在该项
            if (!selectedItems.some(item => item.id === droppedItem.id)) {
                setSelectedItems([...selectedItems, droppedItem]);
            }
        } catch (error) {
            console.error('处理拖放数据错误:', error);
        }
    };

    // 移除已选项
    const removeItem = (id: number) => {
        setSelectedItems(selectedItems.filter(item => item.id !== id));
    };

    // 提交表单 - 手动创建方式
    const handleSubmit = async (values: any) => {
        if (selectedItems.length === 0) {
            message.error('请至少添加一个评测项目');
            return;
        }

        setLoading(true);
        try {
            const response = await axios.post('/api/evaluation_sets', {
                set_name: values.set_name,
                creator_name: values.creator_name,
                description: values.description,
                scene_tag: values.scene_tag, // 添加场景标签
                evaluation_items: selectedItems.map(item => item.id)
            });

            if (response.data.success) {
                message.success('评测集创建成功');
                form.resetFields();
                setSelectedItems([]);
                if (onSuccess) {
                    onSuccess();
                }
            } else {
                message.error('创建失败: ' + response.data.error);
            }
        } catch (error) {
            message.error('请求出错: ' + (error as Error).message);
        } finally {
            setLoading(false);
        }
    };

    // 处理CSV文件上传
    const handleCsvUpload = async (values: any) => {
        setUploadLoading(true);

        const { set_name, creator_name, description, csv_file } = values;

        if (!csv_file || csv_file.length === 0) {
            message.error('请选择CSV文件');
            setUploadLoading(false);
            return;
        }

        const formData = new FormData();
        formData.append('file', csv_file[0].originFileObj);
        formData.append('set_name', set_name);
        formData.append('creator_name', creator_name);
        formData.append('description', description || '');

        try {
            const response = await axios.post('/api/evaluation_set/upload_csv', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            if (response.data.success) {
                message.success(`评测集创建成功，包含 ${response.data.total_cases} 个案例`);
                form.resetFields();
                if (onSuccess) {
                    onSuccess();
                }
            } else {
                // 处理部分成功的情况
                if (response.data.evaluation_set_id) {
                    message.warning(response.data.message);
                } else {
                    message.error('上传失败: ' + response.data.error);
                }
            }
        } catch (error: any) {
            message.error('上传请求出错: ' + (error.response?.data?.detail || error.message));
        } finally {
            setUploadLoading(false);
        }
    };

    // 表单提交处理函数，根据当前激活的标签页决定调用哪个处理函数
    const [activeTab, setActiveTab] = useState('csv');

    const handleFormFinish = (values: any) => {
        if (activeTab === 'manual') {
            handleSubmit(values);
        } else {
            handleCsvUpload(values);
        }
    };

    return (
        <div className="evaluation-creator-container">
            <Card
                title="创建新评测集"
                extra={<Button icon={<CloseOutlined />} onClick={onClose} />}
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleFormFinish}
                >
                    <Form.Item
                        name="set_name"
                        label="评测集名称"
                        rules={[{ required: true, message: '请输入评测集名称' }]}
                    >
                        <Input placeholder="请输入评测集名称" />
                    </Form.Item>

                    <Form.Item
                        name="creator_name"
                        label="创建人"
                        rules={[{ required: true, message: '请输入创建人' }]}
                    >
                        <Input placeholder="请输入创建人" />
                    </Form.Item>

                    <Form.Item
                        name="description"
                        label="描述"
                    >
                        <Input.TextArea
                            placeholder="请输入评测集描述"
                            rows={4}
                        />
                    </Form.Item>

                    {/* 添加场景标签选项 */}
                    <Form.Item
                        name="scene_tag"
                        label="场景标签"
                        help="可以选择多个标签，也可以不选"
                    >
                        <Select
                            mode="multiple"
                            placeholder="请选择场景标签"
                            options={SCENE_TAG_OPTIONS}
                            allowClear
                        />
                    </Form.Item>

                    <Tabs defaultActiveKey="csv" onChange={setActiveTab}>
                        <TabPane tab="手动选择评测项" key="manual">
                            <div className="selection-container">
                                <h3>选择评测项目 ({selectedItems.length})</h3>
                                <div
                                    className="drop-zone"
                                    onDragOver={handleDragOver}
                                    onDragLeave={handleDragLeave}
                                    onDrop={handleDrop}
                                >
                                    {selectedItems.length === 0 ? (
                                        <Empty description="从评测集列表中拖动项目到此处" />
                                    ) : (
                                        <List
                                            size="small"
                                            dataSource={selectedItems}
                                            renderItem={item => (
                                                <List.Item
                                                    key={item.id}
                                                    actions={[
                                                        <Button
                                                            type="text"
                                                            danger
                                                            icon={<CloseOutlined />}
                                                            onClick={() => removeItem(item.id)}
                                                        />
                                                    ]}
                                                >
                                                    <div>
                                                        <strong>{item.pkl_name}</strong>
                                                    </div>
                                                </List.Item>
                                            )}
                                        />
                                    )}
                                </div>
                            </div>
                            <div className="form-actions">
                                <Button onClick={onClose}>
                                    取消
                                </Button>
                                <Button type="primary" htmlType="submit" loading={loading}>
                                    创建评测集
                                </Button>
                            </div>
                        </TabPane>

                        <TabPane tab="上传CSV文件" key="csv">
                            <div className="csv-upload-container">
                                <p className="csv-instructions">
                                    请上传包含评测项目的CSV文件，文件必须包含以下列：
                                    <strong>pkl_name</strong>, <strong>pkl_dir</strong>
                                </p>

                                <Form.Item
                                    name="csv_file"
                                    valuePropName="fileList"
                                    getValueFromEvent={(e) => {
                                        if (Array.isArray(e)) {
                                            return e;
                                        }
                                        return e?.fileList || [];
                                    }}
                                    rules={[{ required: true, message: '请选择CSV文件' }]}
                                >
                                    <Dragger
                                        name="file"
                                        accept=".csv"
                                        beforeUpload={(file) => {
                                            // 检查文件类型
                                            if (!file.name.endsWith('.csv')) {
                                                message.error('只能上传CSV文件!');
                                                return false;
                                            }
                                            return false; // 阻止自动上传
                                        }}
                                        multiple={false}
                                    >
                                        <p className="ant-upload-drag-icon">
                                            <InboxOutlined />
                                        </p>
                                        <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                                        <p className="ant-upload-hint">
                                            支持单个CSV文件上传，文件格式必须符合要求
                                        </p>
                                    </Dragger>
                                </Form.Item>

                                <div className="form-actions">
                                    <Button onClick={onClose}>
                                        取消
                                    </Button>
                                    <Button type="primary" htmlType="submit" loading={uploadLoading}>
                                        上传并创建
                                    </Button>
                                </div>
                            </div>
                        </TabPane>

                    </Tabs>
                </Form>
            </Card>
        </div>
    );
};

export default EvaluationSetCreator;
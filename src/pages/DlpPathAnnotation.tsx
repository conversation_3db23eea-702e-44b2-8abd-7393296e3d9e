import React, { useState, useEffect } from 'react';
import { Layout, List, Card, Button, Input, Pagination, Tag, Space, message, Spin, Radio, Typography, Row, Col } from 'antd';
import { CopyOutlined, SearchOutlined, ExportOutlined, CheckOutlined, CheckCircleOutlined, CloseCircleOutlined, QuestionCircleOutlined, CommentOutlined, WarningOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import ResizableSider from '../components/ResizableSider';
import ReactECharts from 'echarts-for-react';
import './PdpPathAnnotation.css'; // 复用相同的样式
import { EvaluationCase, EvaluationSet } from '../types';
import { useAuth } from '../contexts/AuthContext';

const { Text } = Typography;

interface DlpTrajectoryInfo {
    index: number;
    probability: number;
    vel: number[];
    acc: number[];
    s: number[];
    annotation?: {
        annotation: string;
        employee_id?: string;
        created_at?: string;
        updated_at?: string;
    };
}

interface DlpAnnotationStats {
    total: number;
    annotated: number;
    level0: number; // 标注值为 "0"
    level1: number; // 标注值为 "1"
    level2: number; // 标注值为 "2"
    level3: number; // 标注值为 "3"
    level4: number; // 标注值为 "4"
    level5: number; // 标注值为 "5"
}

const copyToClipboard = async (text: string) => {
    try {
        await navigator.clipboard.writeText(text);
        message.success('路径已复制到剪贴板');
    } catch (err) {
        message.error('复制失败');
    }
};

const DlpPathAnnotation: React.FC = () => {
    const { user, isAuthenticated } = useAuth();
    const { id } = useParams<{ id: string }>();

    useEffect(() => {
        if (!isAuthenticated) {
            message.error('请先登录后再进行标注操作');
            return;
        }
    }, [isAuthenticated]);

    // 状态管理
    const [leftSiderWidth, setLeftSiderWidth] = useState(400);
    const [rightSiderWidth, setRightSiderWidth] = useState(300);
    
    const [annotationStats, setAnnotationStats] = useState<DlpAnnotationStats>({
        total: 0,
        annotated: 0,
        level0: 0,
        level1: 0,
        level2: 0,
        level3: 0,
        level4: 0,
        level5: 0,
    });

    const [evaluationSet, setEvaluationSet] = useState<EvaluationSet | null>(null);
    const [pklList, setPklList] = useState<EvaluationCase[]>([]);
    const [selectedPkl, setSelectedPkl] = useState<EvaluationCase | null>(null);
    const [dlpTrajs, setDlpTrajs] = useState<DlpTrajectoryInfo[]>([]);
    const [highlightTrajIndex, setHighlightTrajIndex] = useState<number | null>(null);
    
    const [loading, setLoading] = useState({
        pklList: false,
        trajectories: false,
        annotation: false,
        markDirty: false,
        checkPkl: false,
    });

    const [pklPagination, setPklPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0
    });

    const [searchKeyword, setSearchKeyword] = useState('');
    const [isDirtyData, setIsDirtyData] = useState(false);
    const [showFullyAnnotatedOnly, setShowFullyAnnotatedOnly] = useState(false);
    const [checkStatusFilter, setCheckStatusFilter] = useState<'all' | 'checked' | 'unchecked'>('all');
    
    const [bagName, setBagName] = useState('');
    const [timeNs, setTimeNs] = useState('');
    const [timeRange, setTimeRange] = useState('');

    // 添加状态来存储最新的标注信息
    const [latestAnnotationInfo, setLatestAnnotationInfo] = useState<{
        employee_id: string;
        updated_at: string;
    } | null>(null);

    // 加载PKL文件列表
    const loadPklList = async (
        page = pklPagination.current, 
        pageSize = pklPagination.pageSize, 
        search = searchKeyword, 
        fullyAnnotatedOnly = showFullyAnnotatedOnly, 
        checkStatus = checkStatusFilter,
        bag_name = bagName,
        time_ns = timeNs,
        time_range = timeRange
    ) => {
        if (!id) return;

        setLoading(prev => ({ ...prev, pklList: true }));
        try {
            const params: any = {
                page,
                per_page: pageSize,
                search,
                fully_annotated_only: fullyAnnotatedOnly,
                check_status: checkStatus
            };

            // 只有当bag_name、时间戳和时间范围都不为空时，才添加这些参数
            if (bag_name && time_ns && time_range) {
                params.bag_name = bag_name;
                params.time_ns = time_ns;
                params.time_range = time_range;
            }

            const response = await axios.get(`/api/evaluation_sets/${id}`, { params });

            if (response.data.success) {
                const evaluationSetData: EvaluationSet = {
                    id: response.data.evaluation_set.id,
                    set_name: response.data.evaluation_set.name,
                    creator_name: response.data.evaluation_set.creator_name || '',
                    description: response.data.evaluation_set.description,
                    cases: response.data.cases || [],
                    case_count: response.data.case_count || 0,
                    created_at: response.data.evaluation_set.created_at
                };
                setEvaluationSet(evaluationSetData);
                setPklList(response.data.cases || []);
                setPklPagination({
                    current: response.data.page || page,
                    pageSize: response.data.per_page || pageSize,
                    total: response.data.case_count || 0
                });
            } else {
                message.error(response.data.error || '加载PKL列表失败');
            }
        } catch (error) {
            console.error('Failed to load PKL list:', error);
            message.error('加载PKL列表出错');
        } finally {
            setLoading(prev => ({ ...prev, pklList: false }));
        }
    };


    // 导出标注数据
    const handleExportAnnotations = async () => {
        if (!id) {
            message.error('缺少评测集ID');
            return;
        }

        try {
            const exportUrl = `/api/annotation/export-dlp-annotations/${id}`;
            window.open(exportUrl, '_blank');

            // const response = await axios.get(`/api/annotation/export-dlp-annotations/${id}`, {
            //     responseType: 'blob'
            // });

            // const blob = new Blob([response.data], { type: 'application/json' });
            // const url = window.URL.createObjectURL(blob);
            // const link = document.createElement('a');
            // link.href = url;
            // link.download = `dlp_annotations_${id}_${new Date().toISOString().split('T')[0]}.json`;
            // document.body.appendChild(link);
            // link.click();
            // document.body.removeChild(link);
            // window.URL.revokeObjectURL(url);

            // message.success('标注数据导出成功');
        } catch (error) {
            console.error('Export failed:', error);
            message.error('导出失败，请稍后再试');
        }
    };

    // 切换PKL检查状态
    const handleTogglePklCheck = async (pkl: EvaluationCase, event: React.MouseEvent) => {
        event.stopPropagation();
        if (!id) {
            message.error('缺少评测集ID');
            return;
        }

        setLoading(prev => ({ ...prev, checkPkl: true }));

        try {
            const isCurrentlyChecked = pkl.is_checked;
            const endpoint = isCurrentlyChecked
                ? `/api/evaluation_sets/${id}/check-pkl/${pkl.id}`
                : `/api/evaluation_sets/${id}/check-pkl/${pkl.id}`;

            const method = isCurrentlyChecked ? 'delete' : 'post';

            const response = await axios[method](endpoint);

            if (response.data.success) {
                // 更新PKL列表中的检查状态
                const updatedPklList = pklList.map(item => {
                    if (item.id === pkl.id) {
                        return {
                            ...item,
                            is_checked: !isCurrentlyChecked,
                            checked_at: isCurrentlyChecked ? undefined : new Date().toISOString(),
                            checked_by: isCurrentlyChecked ? undefined : user?.employee_id
                        };
                    }
                    return item;
                });

                setPklList(updatedPklList);

                // 如果当前选中的PKL就是被操作的PKL，也要更新selectedPkl状态
                if (selectedPkl?.id === pkl.id) {
                    setSelectedPkl({
                        ...selectedPkl,
                        is_checked: !isCurrentlyChecked,
                        checked_at: isCurrentlyChecked ? undefined : new Date().toISOString(),
                        checked_by: isCurrentlyChecked ? undefined : user?.employee_id
                    });
                }

                message.success(
                    isCurrentlyChecked ? 'PKL检查标记已取消' : 'PKL已标记为已检查'
                );
            } else {
                message.error('操作失败');
            }
        } catch (error) {
            console.error('Failed to toggle pkl check:', error);
            message.error('操作出错');
        } finally {
            setLoading(prev => ({ ...prev, checkPkl: false }));
        }
    };

    // 初始加载
    useEffect(() => {
        if (id) {
            loadPklList();
        }
    }, [id]);

    // 当选择PKL文件时加载DLP轨迹
    const handlePklSelect = async (pkl: EvaluationCase) => {
        setSelectedPkl(pkl);
        setHighlightTrajIndex(null);
        setLatestAnnotationInfo(null);
        setLoading(prev => ({ ...prev, trajectories: true }));

        try {
            const response = await axios.get(`/api/annotation/dlp-paths/${pkl.id}`, {
                params: {
                    evaluation_set_id: id
                }
            });
            
            if (response.data.success) {
                const trajectories: DlpTrajectoryInfo[] = response.data.trajs || [];
                setDlpTrajs(trajectories);
                setIsDirtyData(response.data.is_dirty || false);

                // 设置最新的标注信息
                if (response.data.latest_annotation_info) {
                    setLatestAnnotationInfo(response.data.latest_annotation_info);
                }

                // 计算标注统计信息
                const total = trajectories.length;
                let annotated = 0;
                let level0 = 0, level1 = 0, level2 = 0, level3 = 0, level4 = 0, level5 = 0;

                trajectories.forEach((traj) => {
                    if (traj.annotation) {
                        annotated++;
                        switch (traj.annotation.annotation) {
                            case '0': level0++; break;
                            case '1': level1++; break;
                            case '2': level2++; break;
                            case '3': level3++; break;
                            case '4': level4++; break;
                            case '5': level5++; break;
                        }
                    }
                });

                setAnnotationStats({
                    total,
                    annotated,
                    level0,
                    level1,
                    level2,
                    level3,
                    level4,
                    level5
                });
            } else {
                setDlpTrajs([]);
                setAnnotationStats({
                    total: 0,
                    annotated: 0,
                    level0: 0,
                    level1: 0,
                    level2: 0,
                    level3: 0,
                    level4: 0,
                    level5: 0
                });
                message.error('加载DLP轨迹失败');
            }
        } catch (error) {
            console.error('Failed to load DLP trajectories:', error);
            message.error('加载轨迹数据出错');
        } finally {
            setLoading(prev => ({ ...prev, trajectories: false }));
        }
    };

    // 高亮某条轨迹
    const handleHighlightTraj = (trajIndex: number) => {
        setHighlightTrajIndex(trajIndex === highlightTrajIndex ? null : trajIndex);
    };

    // 提交标注
    const handleAnnotate = async (trajIndex: number, annotation: string) => {
        if (!selectedPkl) {
            message.error('请先选择PKL文件');
            return;
        }

        // 检查是否需要删除标注（点击了当前已选择的按钮）
        const currentAnnotation = dlpTrajs[trajIndex]?.annotation?.annotation;
        const isDeleteAction = currentAnnotation === annotation;

        setLoading(prev => ({ ...prev, annotation: true }));
        try {
            const response = await axios.post('/api/annotation/dlp-paths', {
                pkl_id: selectedPkl.id,
                traj_index: trajIndex,
                annotation,
                delete_annotation: isDeleteAction,
                evaluation_set_id: id,
                employee_id: user?.employee_id || null,
            });

            if (response.data.success) {
                // 更新轨迹列表中的标注状态
                const updatedTrajs = [...dlpTrajs];
                const oldAnnotation = updatedTrajs[trajIndex].annotation?.annotation;

                if (isDeleteAction) {
                    updatedTrajs[trajIndex] = {
                        ...updatedTrajs[trajIndex],
                        annotation: undefined
                    };
                    message.success('标注已删除');
                } else {
                    updatedTrajs[trajIndex] = {
                        ...updatedTrajs[trajIndex],
                        annotation: {
                            annotation,
                            employee_id: user?.employee_id || undefined,
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString(),
                        }
                    };
                    message.success('标注保存成功');
                }

                setDlpTrajs(updatedTrajs);

                // 更新统计信息
                setAnnotationStats(prev => {
                    const newStats = { ...prev };

                    if (isDeleteAction && oldAnnotation) {
                        newStats.annotated--;
                        switch (oldAnnotation) {
                            case '0': newStats.level0--; break;
                            case '1': newStats.level1--; break;
                            case '2': newStats.level2--; break;
                            case '3': newStats.level3--; break;
                            case '4': newStats.level4--; break;
                            case '5': newStats.level5--; break;
                        }
                    } else if (!oldAnnotation && !isDeleteAction) {
                        newStats.annotated++;
                        switch (annotation) {
                            case '0': newStats.level0++; break;
                            case '1': newStats.level1++; break;
                            case '2': newStats.level2++; break;
                            case '3': newStats.level3++; break;
                            case '4': newStats.level4++; break;
                            case '5': newStats.level5++; break;
                        }
                    } else if (oldAnnotation !== annotation && !isDeleteAction) {
                        switch (oldAnnotation) {
                            case '0': newStats.level0--; break;
                            case '1': newStats.level1--; break;
                            case '2': newStats.level2--; break;
                            case '3': newStats.level3--; break;
                            case '4': newStats.level4--; break;
                            case '5': newStats.level5--; break;
                        }
                        switch (annotation) {
                            case '0': newStats.level0++; break;
                            case '1': newStats.level1++; break;
                            case '2': newStats.level2++; break;
                            case '3': newStats.level3++; break;
                            case '4': newStats.level4++; break;
                            case '5': newStats.level5++; break;
                        }
                    }

                    return newStats;
                });
            } else {
                message.error('操作失败');
            }
        } catch (error) {
            console.error('Failed to save/delete annotation:', error);
            message.error('操作出错');
        } finally {
            setLoading(prev => ({ ...prev, annotation: false }));
        }
    };

    // 处理标记为脏数据
    const handleMarkAsDirty = async () => {
        if (!selectedPkl) {
            message.error('请先选择PKL文件');
            return;
        }

        setLoading(prev => ({ ...prev, markDirty: true }));
        try {
            const response = await axios.post('/api/annotation/mark-dirty', {
                pkl_id: selectedPkl.id,
                is_dirty: !isDirtyData
            });

            if (response.data.success) {
                setIsDirtyData(response.data.is_dirty);
                message.success(
                    response.data.is_dirty
                        ? '已标记为脏数据'
                        : '已取消脏数据标记'
                );

                const updatedPklList = pklList.map(pkl => {
                    if (pkl.id === selectedPkl.id) {
                        return { ...pkl, dirty_data: response.data.is_dirty };
                    }
                    return pkl;
                });
                setPklList(updatedPklList);
            } else {
                message.error('操作失败');
            }
        } catch (error) {
            console.error('Failed to mark as dirty data:', error);
            message.error('操作出错');
        } finally {
            setLoading(prev => ({ ...prev, markDirty: false }));
        }
    };

    // 处理分页变化
    const handlePklPageChange = (page: number, pageSize?: number) => {
        loadPklList(page, pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, bagName, timeNs, timeRange);
    };

    // 处理搜索
    const handleSearch = () => {
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, bagName, timeNs, timeRange);
    };
    
    const handleFilterToggle = () => {
        const newFilterState = !showFullyAnnotatedOnly;
        setShowFullyAnnotatedOnly(newFilterState);
        loadPklList(1, pklPagination.pageSize, searchKeyword, newFilterState, checkStatusFilter, bagName, timeNs, timeRange);
    };
    
    const handleCheckStatusFilter = (status: 'all' | 'checked' | 'unchecked') => {
        setCheckStatusFilter(status);
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, status, bagName, timeNs, timeRange);
    };

    const handleClearBagTimeFilter = () => {
        setBagName('');
        setTimeNs('');
        setTimeRange('');
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, '', '', '');
    };

    // 生成速度曲线图配置
    const getVelocityChartOption = () => {
        if (!dlpTrajs || dlpTrajs.length === 0) {
            return {
                title: { text: '速度曲线', left: 'center' },
                xAxis: { type: 'category', data: [] },
                yAxis: { type: 'value', name: '速度 (m/s)' },
                series: []
            };
        }

        const series = dlpTrajs.map((traj, index) => ({
            name: `轨迹 ${index + 1}`,
            type: 'line',
            data: traj.vel,
            lineStyle: {
                width: highlightTrajIndex === index ? 4 : 2,
                color: highlightTrajIndex === index ? '#ff4d4f' : undefined
            },
            emphasis: {
                focus: 'series'
            }
        }));

        return {
            title: { text: '速度曲线', left: 'center', textStyle: { fontSize: 14 } },
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'cross' }
            },
            legend: {
                data: dlpTrajs.map((_, index) => `轨迹 ${index + 1}`),
                top: 30,
                type: 'scroll'
            },
            grid: {
                left: '10%',
                right: '10%',
                bottom: '15%',
                top: '25%'
            },
            xAxis: {
                type: 'category',
                data: dlpTrajs[0]?.vel.map((_, i) => i) || [],
                name: '时间步'
            },
            yAxis: {
                type: 'value',
                name: '速度 (m/s)'
            },
            series
        };
    };

    // 生成加速度曲线图配置
    const getAccelerationChartOption = () => {
        if (!dlpTrajs || dlpTrajs.length === 0) {
            return {
                title: { text: '加速度曲线', left: 'center' },
                xAxis: { type: 'category', data: [] },
                yAxis: { type: 'value', name: '加速度 (m/s²)' },
                series: []
            };
        }

        const series = dlpTrajs.map((traj, index) => ({
            name: `轨迹 ${index + 1}`,
            type: 'line',
            data: traj.acc,
            lineStyle: {
                width: highlightTrajIndex === index ? 4 : 2,
                color: highlightTrajIndex === index ? '#ff4d4f' : undefined
            },
            emphasis: {
                focus: 'series'
            }
        }));

        return {
            title: { text: '加速度曲线', left: 'center', textStyle: { fontSize: 14 } },
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'cross' }
            },
            legend: {
                data: dlpTrajs.map((_, index) => `轨迹 ${index + 1}`),
                top: 30,
                type: 'scroll'
            },
            grid: {
                left: '10%',
                right: '10%',
                bottom: '15%',
                top: '25%'
            },
            xAxis: {
                type: 'category',
                data: dlpTrajs[0]?.acc.map((_, i) => i) || [],
                name: '时间步'
            },
            yAxis: {
                type: 'value',
                name: '加速度 (m/s²)'
            },
            series
        };
    };

    // 生成距离曲线图配置
    const getDistanceChartOption = () => {
        if (!dlpTrajs || dlpTrajs.length === 0) {
            return {
                title: { text: '距离曲线', left: 'center' },
                xAxis: { type: 'category', data: [] },
                yAxis: { type: 'value', name: '距离 (m)' },
                series: []
            };
        }

        const series = dlpTrajs.map((traj, index) => ({
            name: `轨迹 ${index + 1}`,
            type: 'line',
            data: traj.s,
            lineStyle: {
                width: highlightTrajIndex === index ? 4 : 2,
                color: highlightTrajIndex === index ? '#ff4d4f' : undefined
            },
            emphasis: {
                focus: 'series'
            }
        }));

        return {
            title: { text: '距离曲线', left: 'center', textStyle: { fontSize: 14 } },
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'cross' }
            },
            legend: {
                data: dlpTrajs.map((_, index) => `轨迹 ${index + 1}`),
                top: 30,
                type: 'scroll'
            },
            grid: {
                left: '10%',
                right: '10%',
                bottom: '15%',
                top: '25%'
            },
            xAxis: {
                type: 'category',
                data: dlpTrajs[0]?.s.map((_, i) => i) || [],
                name: '时间步'
            },
            yAxis: {
                type: 'value',
                name: '距离 (m)'
            },
            series
        };
    };

    return (
        <div className="pdp-path-annotation-layout" style={{ display: 'flex', height: '100vh' }}>
            {/* 左侧PKL列表 */}
            <ResizableSider
                width={leftSiderWidth}
                minWidth={200}
                maxWidth={600}
                onResize={setLeftSiderWidth}
                position="left"
                className="pkl-list-sider"
            >
                <div className="pkl-list-header">
                    <Space direction="vertical" style={{ width: '100%' }} size="small">
                        {evaluationSet && (
                            <div style={{ 
                                marginBottom: '4px', 
                                padding: '4px 6px', 
                                background: '#f0f0f0', 
                                borderRadius: '3px',
                                border: '1px solid #d9d9d9'
                            }}>
                                <Text strong style={{ fontSize: '12px', color: '#1890ff', lineHeight: '1.2' }}>
                                    {evaluationSet.set_name}
                                </Text>
                            </div>
                        )}
                        
                        <Input
                            placeholder="搜索PKL文件"
                            prefix={<SearchOutlined />}
                            value={searchKeyword}
                            onChange={e => setSearchKeyword(e.target.value)}
                            onPressEnter={handleSearch}
                            size="small"
                            style={{ marginBottom: '4px' }}
                        />
                        
                        <div style={{ border: '1px solid #d9d9d9', padding: '6px', borderRadius: '4px' }}>
                            <Text strong style={{ fontSize: '11px', color: '#666' }}>bag_name时间戳过滤:</Text>
                            <Input
                                placeholder="bag_name"
                                value={bagName}
                                onChange={e => setBagName(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Input
                                placeholder="时间戳(纳秒)"
                                value={timeNs}
                                onChange={e => setTimeNs(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Input
                                placeholder="时间范围(秒)"
                                value={timeRange}
                                onChange={e => setTimeRange(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Space size="small" style={{ marginTop: '3px', width: '100%' }}>
                                <Button
                                    type="primary"
                                    size="small"
                                    onClick={handleSearch}
                                    style={{ flex: 1 }}
                                >
                                    应用过滤
                                </Button>
                                <Button
                                    size="small"
                                    onClick={handleClearBagTimeFilter}
                                    style={{ flex: 1 }}
                                >
                                    清空
                                </Button>
                            </Space>
                        </div>

                        <Space size="small" style={{ marginTop: '4px' }}>
                            <Button
                                type={showFullyAnnotatedOnly ? "primary" : "default"}
                                icon={<CheckCircleOutlined />}
                                onClick={handleFilterToggle}
                                size="small"
                            >
                                {showFullyAnnotatedOnly ? "显示全部" : "仅显示完全标注"}
                            </Button>
                            <Radio.Group
                                value={checkStatusFilter}
                                onChange={e => handleCheckStatusFilter(e.target.value)}
                                size="small"
                                buttonStyle="solid"
                            >
                                <Radio.Button value="all">全部</Radio.Button>
                                <Radio.Button value="checked">已检查</Radio.Button>
                                <Radio.Button value="unchecked">未检查</Radio.Button>
                            </Radio.Group>
                        </Space>
                        <Button
                            type="default"
                            icon={<ExportOutlined />}
                            onClick={handleExportAnnotations}
                            size="small"
                            style={{ width: '100%', marginTop: '4px' }}
                        >
                            导出DLP标注数据
                        </Button>
                    </Space>
                </div>

                <List
                    loading={loading.pklList}
                    dataSource={pklList}
                    renderItem={item => (
                        <List.Item
                            className={selectedPkl?.id === item.id ? 'pkl-item selected' : 'pkl-item'}
                            onClick={() => handlePklSelect(item)}
                            style={{ display: 'flex', alignItems: 'flex-start' }}
                        >
                            <div className="pkl-item-content" style={{ flex: 1, minWidth: 0 }}>
                                <div className="pkl-name" style={{
                                    fontSize: '11px',
                                    lineHeight: '1.3',
                                    wordBreak: 'break-all',
                                    whiteSpace: 'normal',
                                    maxWidth: '100%',
                                    marginBottom: '4px'
                                }}>
                                    {item.pkl_name}
                                    {item.dirty_data && (
                                        <Tag color="red" style={{ marginLeft: 5, fontSize: '10px' }}>
                                            <WarningOutlined /> 脏数据
                                        </Tag>
                                    )}
                                    {item.is_checked && (
                                        <Tag color="green" style={{ marginLeft: 5, fontSize: '10px' }}>
                                            <CheckOutlined /> 已检查
                                        </Tag>
                                    )}
                                </div>
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'row', gap: 4, marginLeft: 8 }}>
                                <Button
                                    type={item.is_checked ? "primary" : "default"}
                                    icon={<CheckOutlined />}
                                    size="small"
                                    loading={loading.checkPkl}
                                    onClick={(e) => handleTogglePklCheck(item, e)}
                                    title={item.is_checked ? "取消检查标记" : "标记为已检查"}
                                    style={{
                                        flexShrink: 0,
                                        backgroundColor: item.is_checked ? '#52c41a' : undefined,
                                        borderColor: item.is_checked ? '#52c41a' : undefined,
                                    }}
                                />
                                <Button
                                    type="text"
                                    icon={<CopyOutlined />}
                                    size="small"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        const fullPath = `${item.pkl_dir}/${item.pkl_name}`;
                                        copyToClipboard(fullPath);
                                    }}
                                    title="复制文件路径"
                                    style={{ flexShrink: 0 }}
                                />
                            </div>
                        </List.Item>
                    )}
                />

                <div className="pkl-pagination">
                    <Pagination
                        current={pklPagination.current}
                        pageSize={pklPagination.pageSize}
                        total={pklPagination.total}
                        onChange={handlePklPageChange}
                        showSizeChanger={false}
                        size="small"
                        simple
                    />
                </div>
            </ResizableSider>

            {/* 中间曲线图区域 */}
            <div 
                className="visualization-content"
                style={{ 
                    flex: 1,
                    background: '#fff',
                    borderRight: '1px solid #f0f0f0',
                    overflow: 'hidden',
                    display: 'flex',
                    flexDirection: 'column'
                }}
            >
                {selectedPkl ? (
                    <div style={{ width: '100%', height: '100%', padding: '16px' }}>
                        {/* 显示最新的标注信息 */}
                        {latestAnnotationInfo && (
                            <div style={{ 
                                padding: '8px', 
                                background: '#f6ffed', 
                                borderBottom: '1px solid #f0f0f0',
                                fontSize: '12px',
                                marginBottom: '16px'
                            }}>
                                <Text strong>最新标注信息：</Text>
                                <Text> 标注员 {latestAnnotationInfo.employee_id} </Text>
                                <Text>于 {new Date(latestAnnotationInfo.updated_at).toLocaleString('zh-CN')} 更新</Text>
                            </div>
                        )}

                        {loading.trajectories ? (
                            <div style={{ 
                                display: 'flex', 
                                justifyContent: 'center', 
                                alignItems: 'center', 
                                height: '100%' 
                            }}>
                                <Spin tip="加载轨迹数据..." />
                            </div>
                        ) : dlpTrajs.length === 0 ? (
                            <div style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: '100%',
                                color: '#999'
                            }}>
                                <Text>未找到DLP轨迹数据</Text>
                            </div>
                        ) : (
                            <Row gutter={[16, 16]} style={{ height: '100%' }}>
                                <Col span={24} style={{ height: '33.33%' }}>
                                    <Card 
                                        title="速度曲线" 
                                        size="small" 
                                        style={{ height: '100%' }}
                                        bodyStyle={{ height: 'calc(100% - 40px)', padding: '8px' }}
                                    >
                                        <ReactECharts
                                            option={getVelocityChartOption()}
                                            style={{ height: '100%', width: '100%' }}
                                            onEvents={{
                                                'legendselectchanged': (params: any) => {
                                                    // 处理图例点击事件，可以用来高亮轨迹
                                                }
                                            }}
                                        />
                                    </Card>
                                </Col>
                                <Col span={24} style={{ height: '33.33%' }}>
                                    <Card 
                                        title="加速度曲线" 
                                        size="small" 
                                        style={{ height: '100%' }}
                                        bodyStyle={{ height: 'calc(100% - 40px)', padding: '8px' }}
                                    >
                                        <ReactECharts
                                            option={getAccelerationChartOption()}
                                            style={{ height: '100%', width: '100%' }}
                                        />
                                    </Card>
                                </Col>
                                <Col span={24} style={{ height: '33.33%' }}>
                                    <Card 
                                        title="距离曲线" 
                                        size="small" 
                                        style={{ height: '100%' }}
                                        bodyStyle={{ height: 'calc(100% - 40px)', padding: '8px' }}
                                    >
                                        <ReactECharts
                                            option={getDistanceChartOption()}
                                            style={{ height: '100%', width: '100%' }}
                                        />
                                    </Card>
                                </Col>
                            </Row>
                        )}
                    </div>
                ) : (
                    <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                        color: '#999'
                    }}>
                        <Text>请从左侧选择一个PKL文件</Text>
                    </div>
                )}
            </div>

            {/* 右侧标注面板 */}
            <ResizableSider
                width={rightSiderWidth}
                minWidth={250}
                maxWidth={500}
                onResize={setRightSiderWidth}
                position="right"
                className="annotation-sider"
            >
                <div className="annotation-panel">
                    <div className="annotation-header">
                        {selectedPkl && (
                            <div className="bad-data-button-container">
                                <Button
                                    danger={!isDirtyData}
                                    type={isDirtyData ? "default" : "primary"}
                                    icon={<WarningOutlined />}
                                    onClick={handleMarkAsDirty}
                                    loading={loading.markDirty}
                                >
                                    {isDirtyData ? "取消脏数据标记" : "标记为脏数据"}
                                </Button>
                            </div>
                        )}
                        {selectedPkl && !loading.trajectories && dlpTrajs.length > 0 && (
                            <div className="annotation-stats">
                                <div className="stats-summary">
                                    <Tag color="purple">0级: {annotationStats.level0}</Tag>
                                    <Tag color="blue">1级: {annotationStats.level1}</Tag>
                                    <Tag color="cyan">2级: {annotationStats.level2}</Tag>
                                    <Tag color="green">3级: {annotationStats.level3}</Tag>
                                    <Tag color="orange">4级: {annotationStats.level4}</Tag>
                                    <Tag color="red">5级: {annotationStats.level5}</Tag>
                                </div>
                            </div>
                        )}
                    </div>

                    {!selectedPkl ? (
                        <div className="empty-annotation">
                            <Text>请先选择一个PKL文件</Text>
                        </div>
                    ) : loading.trajectories ? (
                        <div className="loading-paths">
                            <Spin tip="加载轨迹数据..." />
                        </div>
                    ) : dlpTrajs.length === 0 ? (
                        <div className="no-paths">
                            <Text>未找到DLP轨迹数据</Text>
                        </div>
                    ) : (
                        <div className="paths-list">
                            {dlpTrajs.map((traj) => (
                                <div className="path-card-container" key={traj.index}>
                                    <Card
                                        className={highlightTrajIndex === traj.index ? 'path-card highlighted' : 'path-card'}
                                        size="small"
                                        onClick={() => handleHighlightTraj(traj.index)}
                                        hoverable
                                    >
                                        <div className="path-info">
                                            <div className="path-title">
                                                <Tag color="blue">轨迹 {traj.index + 1}</Tag>
                                                <Text style={{ fontSize: '11px', marginLeft: '8px' }}>
                                                    概率: {(traj.probability * 100).toFixed(1)}%
                                                </Text>
                                            </div>
                                        </div>
                                    </Card>

                                    <div className="path-actions" onClick={e => e.stopPropagation()}>
                                        <Radio.Group
                                            value={traj.annotation?.annotation}
                                            onChange={e => {
                                                e.stopPropagation();
                                                handleAnnotate(traj.index, e.target.value);
                                            }}
                                            buttonStyle="solid"
                                            style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}
                                        >
                                            <Radio.Button
                                                value="0"
                                                className={traj.annotation?.annotation === '0' ? 'level0-selected' : ''}
                                                style={{ flex: '0 0 48%', textAlign: 'center' }}
                                                onClick={e => {
                                                    e.stopPropagation();
                                                    if (traj.annotation?.annotation === '0') {
                                                        handleAnnotate(traj.index, '0');
                                                    }
                                                }}
                                            >
                                                0级
                                            </Radio.Button>
                                            <Radio.Button
                                                value="1"
                                                className={traj.annotation?.annotation === '1' ? 'level1-selected' : ''}
                                                style={{ flex: '0 0 48%', textAlign: 'center' }}
                                                onClick={e => {
                                                    e.stopPropagation();
                                                    if (traj.annotation?.annotation === '1') {
                                                        handleAnnotate(traj.index, '1');
                                                    }
                                                }}
                                            >
                                                1级
                                            </Radio.Button>
                                            <Radio.Button
                                                value="2"
                                                className={traj.annotation?.annotation === '2' ? 'level2-selected' : ''}
                                                style={{ flex: '0 0 48%', textAlign: 'center' }}
                                                onClick={e => {
                                                    e.stopPropagation();
                                                    if (traj.annotation?.annotation === '2') {
                                                        handleAnnotate(traj.index, '2');
                                                    }
                                                }}
                                            >
                                                2级
                                            </Radio.Button>
                                            <Radio.Button
                                                value="3"
                                                className={traj.annotation?.annotation === '3' ? 'level3-selected' : ''}
                                                style={{ flex: '0 0 48%', textAlign: 'center' }}
                                                onClick={e => {
                                                    e.stopPropagation();
                                                    if (traj.annotation?.annotation === '3') {
                                                        handleAnnotate(traj.index, '3');
                                                    }
                                                }}
                                            >
                                                3级
                                            </Radio.Button>
                                            <Radio.Button
                                                value="4"
                                                className={traj.annotation?.annotation === '4' ? 'level4-selected' : ''}
                                                style={{ flex: '0 0 48%', textAlign: 'center' }}
                                                onClick={e => {
                                                    e.stopPropagation();
                                                    if (traj.annotation?.annotation === '4') {
                                                        handleAnnotate(traj.index, '4');
                                                    }
                                                }}
                                            >
                                                4级
                                            </Radio.Button>
                                            <Radio.Button
                                                value="5"
                                                className={traj.annotation?.annotation === '5' ? 'level5-selected' : ''}
                                                style={{ flex: '0 0 48%', textAlign: 'center' }}
                                                onClick={e => {
                                                    e.stopPropagation();
                                                    if (traj.annotation?.annotation === '5') {
                                                        handleAnnotate(traj.index, '5');
                                                    }
                                                }}
                                            >
                                                5级
                                            </Radio.Button>
                                        </Radio.Group>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </ResizableSider>
        </div>
    );
};

export default DlpPathAnnotation;
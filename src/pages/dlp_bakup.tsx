import React, { useState, useEffect } from 'react';
import { Layout, List, Card, Button, Input, Pagination, Tag, Space, message, Spin, Radio, Typography } from 'antd';
import { CopyOutlined, SearchOutlined, ExportOutlined, CheckOutlined, CheckCircleOutlined, CloseCircleOutlined, QuestionCircleOutlined, WarningOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import ResizableSider from '../components/ResizableSider';
import ReactECharts from 'echarts-for-react';
import './PdpPathAnnotation.css'; // 复用相同的样式
import { EvaluationCase, EvaluationSet } from '../types';
import { useAuth } from '../contexts/AuthContext';

const { Text } = Typography;

// DLP轨迹信息接口
interface DlpTrajInfo {
    index: number;
    probability: number;
    vel: number[];
    acc: number[];
    s: number[];
    annotation?: {
        annotation: string;
    };
}

// 标注统计接口
interface AnnotationStats {
    total: number;
    annotated: number;
    type0: number;
    type1: number;
    type2: number;
    type3: number;
    type4: number;
    type5: number;
}

// 复制到剪贴板的函数
const copyToClipboard = async (text: string) => {
    try {
        await navigator.clipboard.writeText(text);
        message.success('已复制到剪贴板');
    } catch (err) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        message.success('已复制到剪贴板');
    }
};

const DlpPathAnnotation: React.FC = () => {
    // 获取URL参数中的评测集ID
    const { user, isAuthenticated } = useAuth();
    const { id } = useParams<{ id: string }>();

    // 状态管理
    const [leftSiderWidth, setLeftSiderWidth] = useState(400);
    const [rightSiderWidth, setRightSiderWidth] = useState(300);
    
    const [annotationStats, setAnnotationStats] = useState<AnnotationStats>({
        total: 0,
        annotated: 0,
        type0: 0,
        type1: 0,
        type2: 0,
        type3: 0,
        type4: 0,
        type5: 0,
    });
    
    const [evaluationSet, setEvaluationSet] = useState<EvaluationSet | null>(null);
    const [pklList, setPklList] = useState<EvaluationCase[]>([]);
    const [selectedPkl, setSelectedPkl] = useState<EvaluationCase | null>(null);
    const [dlpTrajs, setDlpTrajs] = useState<DlpTrajInfo[]>([]);
    const [highlightTrajIndex, setHighlightTrajIndex] = useState<number | null>(null);
    
    const [loading, setLoading] = useState({
        pklList: false,
        trajs: false,
        annotation: false,
        markDirty: false,
        checkPkl: false,
    });
    
    const [pklPagination, setPklPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0
    });
    
    const [searchKeyword, setSearchKeyword] = useState('');
    const [isDirtyData, setIsDirtyData] = useState(false);
    const [showFullyAnnotatedOnly, setShowFullyAnnotatedOnly] = useState(false);
    const [checkStatusFilter, setCheckStatusFilter] = useState<'all' | 'checked' | 'unchecked'>('all');
    
    // bag_name和时间戳过滤
    const [bagName, setBagName] = useState('');
    const [timeNs, setTimeNs] = useState('');
    const [timeRange, setTimeRange] = useState('');
    
    // 最新标注信息
    const [latestAnnotationInfo, setLatestAnnotationInfo] = useState<{
        employee_id: string;
        updated_at: string;
    } | null>(null);

    // 加载PKL文件列表
    const loadPklList = async (
        page = pklPagination.current, 
        pageSize = pklPagination.pageSize, 
        search = searchKeyword, 
        fullyAnnotatedOnly = showFullyAnnotatedOnly, 
        checkStatus = checkStatusFilter,
        bag_name = bagName,
        time_ns = timeNs,
        time_range = timeRange
    ) => {
        try {
            setLoading(prev => ({ ...prev, pklList: true }));
            
            const params = new URLSearchParams({
                page: page.toString(),
                page_size: pageSize.toString(),
                ...(search && { search }),
                ...(fullyAnnotatedOnly && { fully_annotated_only: 'true' }),
                ...(checkStatus !== 'all' && { check_status: checkStatus }),
                ...(bag_name && { vin: bag_name }),
                ...(time_ns && { time_ns }),
                ...(time_range && { time_range }),
            });

            const response = await axios.get(`/api/annotation/dlp/${id}/cases?${params}`);
            
            if (response.data.success) {
                setPklList(response.data.data);
                setPklPagination(prev => ({
                    ...prev,
                    current: page,
                    pageSize: pageSize,
                    total: response.data.total
                }));
                
                // 获取评测集信息
                if (response.data.evaluation_set) {
                    setEvaluationSet(response.data.evaluation_set);
                }
            }
        } catch (error) {
            console.error('加载PKL列表失败:', error);
            message.error('加载PKL列表失败');
        } finally {
            setLoading(prev => ({ ...prev, pklList: false }));
        }
    };

    // 导出标注数据
    const handleExportAnnotations = async () => {
        if (!id) return;

        try {
            const response = await axios.get(`/api/annotation/export-dlp-annotations/${id}`);
            if (response.data.success) {
                const jsonStr = JSON.stringify(response.data.annotations, null, 2);
                const blob = new Blob([jsonStr], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `dlp_annotations_set_${id}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                message.success('标注数据导出成功');
            }
        } catch (error) {
            console.error('导出标注数据失败:', error);
            message.error('导出标注数据失败');
        }
    };

    // 切换PKL检查状态
    const handleTogglePklCheck = async (pkl: EvaluationCase, event: React.MouseEvent) => {
        event.stopPropagation();
        if (!id) return;

        setLoading(prev => ({ ...prev, checkPkl: true }));

        try {
            const response = await axios.post('/api/evaluation-set/toggle-case-check', {
                evaluation_set_id: parseInt(id),
                case_id: pkl.id,
                is_checked: !pkl.is_checked
            });

            if (response.data.success) {
                // 更新列表中的检查状态
                const updatedPklList = pklList.map(item => {
                    if (item.id === pkl.id) {
                        return { ...item, is_checked: !item.is_checked };
                    }
                    return item;
                });
                setPklList(updatedPklList);
                
                message.success(
                    pkl.is_checked ? '已取消检查标记' : '已标记为检查完成'
                );
            }
        } catch (error) {
            console.error('切换检查状态失败:', error);
            message.error('操作失败');
        } finally {
            setLoading(prev => ({ ...prev, checkPkl: false }));
        }
    };

    // 初始加载
    useEffect(() => {
        if (id) {
            loadPklList();
        }
    }, [id]);

    // 选择PKL文件时加载DLP轨迹
    const handlePklSelect = async (pkl: EvaluationCase) => {
        setSelectedPkl(pkl);
        setHighlightTrajIndex(null);
        setLatestAnnotationInfo(null);
        setLoading(prev => ({ ...prev, trajs: true }));

        try {
            const response = await axios.get(`/api/annotation/dlp-paths/${pkl.id}`, {
                params: {
                    evaluation_set_id: id
                }
            });
            
            if (response.data.success) {
                setDlpTrajs(response.data.trajs || []);
                setIsDirtyData(response.data.is_dirty);
                setLatestAnnotationInfo(response.data.latest_annotation_info);
                
                // 计算标注统计
                const trajs = response.data.trajs || [];
                const stats = {
                    total: trajs.length,
                    annotated: trajs.filter((t: DlpTrajInfo) => t.annotation).length,
                    type0: trajs.filter((t: DlpTrajInfo) => t.annotation?.annotation === '0').length,
                    type1: trajs.filter((t: DlpTrajInfo) => t.annotation?.annotation === '1').length,
                    type2: trajs.filter((t: DlpTrajInfo) => t.annotation?.annotation === '2').length,
                    type3: trajs.filter((t: DlpTrajInfo) => t.annotation?.annotation === '3').length,
                    type4: trajs.filter((t: DlpTrajInfo) => t.annotation?.annotation === '4').length,
                    type5: trajs.filter((t: DlpTrajInfo) => t.annotation?.annotation === '5').length,
                };
                setAnnotationStats(stats);
            } else {
                message.error('加载DLP轨迹失败');
            }
        } catch (error) {
            console.error('加载DLP轨迹失败:', error);
            message.error('加载DLP轨迹失败');
        } finally {
            setLoading(prev => ({ ...prev, trajs: false }));
        }
    };

    // 高亮某条轨迹
    const handleHighlightTraj = (trajIndex: number) => {
        setHighlightTrajIndex(trajIndex === highlightTrajIndex ? null : trajIndex);
    };

    // 提交标注
    const handleAnnotate = async (trajIndex: number, annotation: string) => {
        if (!selectedPkl) {
            message.error('请先选择PKL文件');
            return;
        }

        // 检查是否需要删除标注
        const currentAnnotation = dlpTrajs[trajIndex]?.annotation?.annotation;
        const isDeleteAction = currentAnnotation === annotation;

        setLoading(prev => ({ ...prev, annotation: true }));
        try {
            const response = await axios.post('/api/annotation/dlp-paths', {
                pkl_id: selectedPkl.id,
                traj_index: trajIndex,
                annotation,
                delete_annotation: isDeleteAction,
                evaluation_set_id: id,
                employee_id: user?.employee_id || null,
            });

            if (response.data.success) {
                // 更新轨迹列表中的标注状态
                const updatedTrajs = [...dlpTrajs];
                const oldAnnotation = updatedTrajs[trajIndex].annotation?.annotation;

                if (isDeleteAction) {
                    updatedTrajs[trajIndex].annotation = undefined;
                } else {
                    updatedTrajs[trajIndex].annotation = { annotation };
                }

                setDlpTrajs(updatedTrajs);

                // 更新统计信息
                setAnnotationStats(prev => {
                    const newStats = { ...prev };
                    
                    if (isDeleteAction && oldAnnotation) {
                        newStats.annotated -= 1;
                        newStats[`type${oldAnnotation}` as keyof AnnotationStats] -= 1;
                    } else if (!oldAnnotation && !isDeleteAction) {
                        newStats.annotated += 1;
                        newStats[`type${annotation}` as keyof AnnotationStats] += 1;
                    } else if (oldAnnotation !== annotation && !isDeleteAction) {
                        newStats[`type${oldAnnotation}` as keyof AnnotationStats] -= 1;
                        newStats[`type${annotation}` as keyof AnnotationStats] += 1;
                    }

                    return newStats;
                });

                message.success(isDeleteAction ? '标注已删除' : '标注已保存');
            } else {
                message.error('操作失败');
            }
        } catch (error) {
            console.error('标注操作失败:', error);
            message.error('操作出错');
        } finally {
            setLoading(prev => ({ ...prev, annotation: false }));
        }
    };

    // 处理标记为脏数据
    const handleMarkAsDirty = async () => {
        if (!selectedPkl) {
            message.error('请先选择PKL文件');
            return;
        }

        setLoading(prev => ({ ...prev, markDirty: true }));
        try {
            const response = await axios.post('/api/annotation/mark-dirty', {
                pkl_id: selectedPkl.id,
                is_dirty: !isDirtyData
            });

            if (response.data.success) {
                setIsDirtyData(response.data.is_dirty);
                message.success(
                    response.data.is_dirty
                        ? '已标记为脏数据'
                        : '已取消脏数据标记'
                );

                // 更新列表中当前项的脏数据状态
                const updatedPklList = pklList.map(pkl => {
                    if (pkl.id === selectedPkl.id) {
                        return { ...pkl, dirty_data: response.data.is_dirty };
                    }
                    return pkl;
                });
                setPklList(updatedPklList);
            } else {
                message.error('操作失败');
            }
        } catch (error) {
            console.error('标记脏数据失败:', error);
            message.error('操作出错');
        } finally {
            setLoading(prev => ({ ...prev, markDirty: false }));
        }
    };

    // 处理分页变化
    const handlePklPageChange = (page: number, pageSize?: number) => {
        loadPklList(page, pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, bagName, timeNs, timeRange);
    };

    // 处理搜索
    const handleSearch = () => {
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, bagName, timeNs, timeRange);
    };
    
    const handleFilterToggle = () => {
        const newFilterState = !showFullyAnnotatedOnly;
        setShowFullyAnnotatedOnly(newFilterState);
        loadPklList(1, pklPagination.pageSize, searchKeyword, newFilterState, checkStatusFilter, bagName, timeNs, timeRange);
    };
    
    const handleCheckStatusFilter = (status: 'all' | 'checked' | 'unchecked') => {
        setCheckStatusFilter(status);
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, status, bagName, timeNs, timeRange);
    };

    // 清空bag_name和时间戳过滤
    const handleClearBagTimeFilter = () => {
        setBagName('');
        setTimeNs('');
        setTimeRange('');
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, '', '', '');
    };

    // 生成图表配置
    const getChartOption = (data: number[], title: string, color: string, yAxisName: string) => {
        return {
            title: {
                text: title,
                left: 'center',
                textStyle: {
                    fontSize: 14
                }
            },
            grid: {
                left: '10%',
                right: '10%',
                bottom: '15%',
                top: '20%'
            },
            xAxis: {
                type: 'category',
                data: data.map((_, index) => index),
                name: '时间步',
                nameLocation: 'middle',
                nameGap: 25
            },
            yAxis: {
                type: 'value',
                name: yAxisName,
                nameLocation: 'middle',
                nameGap: 35
            },
            series: [{
                data: data,
                type: 'line',
                smooth: true,
                lineStyle: {
                    color: color,
                    width: 2
                },
                itemStyle: {
                    color: color
                },
                symbol: 'circle',
                symbolSize: 4
            }],
            tooltip: {
                trigger: 'axis',
                formatter: (params: any) => {
                    const point = params[0];
                    return `时间步: ${point.dataIndex}<br/>${yAxisName}: ${point.value.toFixed(3)}`;
                }
            }
        };
    };

    return (
        <div className="pdp-path-annotation-layout" style={{ display: 'flex', height: '100vh' }}>
            {/* 左侧PKL列表 - 可调整宽度 */}
            <ResizableSider
                width={leftSiderWidth}
                minWidth={200}
                maxWidth={600}
                onResize={setLeftSiderWidth}
                position="left"
                className="pkl-list-sider"
            >
                <div className="pkl-list-header">
                    <Space direction="vertical" style={{ width: '100%' }} size="small">
                        {/* 评测集名称显示区域 */}
                        {evaluationSet && (
                            <div style={{ 
                                marginBottom: '4px', 
                                padding: '4px 6px', 
                                background: '#f0f0f0', 
                                borderRadius: '3px',
                                border: '1px solid #d9d9d9'
                            }}>
                                <Text strong style={{ fontSize: '12px', color: '#1890ff', lineHeight: '1.2' }}>
                                    {evaluationSet.set_name}
                                </Text>
                            </div>
                        )}
                        
                        {/* 搜索框 */}
                        <Input
                            placeholder="搜索PKL文件"
                            prefix={<SearchOutlined />}
                            value={searchKeyword}
                            onChange={e => setSearchKeyword(e.target.value)}
                            onPressEnter={handleSearch}
                            size="small"
                            style={{ marginBottom: '4px' }}
                        />
                        
                        {/* bag_name和时间戳过滤输入框 */}
                        <div style={{ border: '1px solid #d9d9d9', padding: '6px', borderRadius: '4px' }}>
                            <Text strong style={{ fontSize: '11px', color: '#666' }}>bag_name时间戳过滤:</Text>
                            <Input
                                placeholder="bag_name (例: LFZ63AZ52SD000199_record_data_2025_05_04_08_40_39)"
                                value={bagName}
                                onChange={e => setBagName(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Input
                                placeholder="时间戳(纳秒)"
                                value={timeNs}
                                onChange={e => setTimeNs(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Input
                                placeholder="时间范围(秒)"
                                value={timeRange}
                                onChange={e => setTimeRange(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Space size="small" style={{ marginTop: '3px', width: '100%' }}>
                                <Button
                                    type="primary"
                                    size="small"
                                    onClick={handleSearch}
                                    style={{ flex: 1 }}
                                >
                                    应用过滤
                                </Button>
                                <Button
                                    size="small"
                                    onClick={handleClearBagTimeFilter}
                                    style={{ flex: 1 }}
                                >
                                    清空
                                </Button>
                            </Space>
                        </div>

                        <Space size="small" style={{ marginTop: '4px' }}>
                            <Button
                                type={showFullyAnnotatedOnly ? "primary" : "default"}
                                icon={<CheckCircleOutlined />}
                                onClick={handleFilterToggle}
                                size="small"
                            >
                                {showFullyAnnotatedOnly ? "显示全部" : "仅显示完全标注"}
                            </Button>
                            <Radio.Group
                                value={checkStatusFilter}
                                onChange={e => handleCheckStatusFilter(e.target.value)}
                                size="small"
                                buttonStyle="solid"
                            >
                                <Radio.Button value="all">全部</Radio.Button>
                                <Radio.Button value="checked">已检查</Radio.Button>
                                <Radio.Button value="unchecked">未检查</Radio.Button>
                            </Radio.Group>
                            <Button
                                type="default"
                                icon={<SearchOutlined />}
                                onClick={handleSearch}
                                size="small"
                            >
                                搜索
                            </Button>
                        </Space>
                        <Button
                            type="default"
                            icon={<ExportOutlined />}
                            onClick={handleExportAnnotations}
                            size="small"
                            style={{ width: '100%', marginTop: '4px' }}
                        >
                            导出DLP标注数据
                        </Button>
                    </Space>
                </div>

                <List
                    loading={loading.pklList}
                    dataSource={pklList}
                    renderItem={item => (
                        <List.Item
                            className={selectedPkl?.id === item.id ? 'pkl-item selected' : 'pkl-item'}
                            onClick={() => handlePklSelect(item)}
                            style={{ display: 'flex', alignItems: 'flex-start' }}
                        >
                            <div className="pkl-item-content" style={{ flex: 1, minWidth: 0 }}>
                                <div className="pkl-name" style={{
                                    fontSize: '11px',
                                    lineHeight: '1.3',
                                    wordBreak: 'break-all',
                                    whiteSpace: 'normal',
                                    maxWidth: '100%',
                                    marginBottom: '4px'
                                }}>
                                    {item.pkl_name}
                                    {item.dirty_data && (
                                        <Tag color="red" style={{ marginLeft: 5, fontSize: '10px' }}>
                                            <WarningOutlined /> 脏数据
                                        </Tag>
                                    )}
                                    {item.is_checked && (
                                        <Tag color="green" style={{ marginLeft: 5, fontSize: '10px' }}>
                                            <CheckOutlined /> 已检查
                                        </Tag>
                                    )}
                                </div>
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'row', gap: 4, marginLeft: 8 }}>
                                <Button
                                    type={item.is_checked ? "primary" : "default"}
                                    icon={<CheckOutlined />}
                                    size="small"
                                    loading={loading.checkPkl}
                                    onClick={(e) => handleTogglePklCheck(item, e)}
                                    title={item.is_checked ? "取消检查标记" : "标记为已检查"}
                                    style={{
                                        flexShrink: 0,
                                        backgroundColor: item.is_checked ? '#52c41a' : undefined,
                                        borderColor: item.is_checked ? '#52c41a' : undefined,
                                    }}
                                />
                                <Button
                                    type="text"
                                    icon={<CopyOutlined />}
                                    size="small"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        const fullPath = `${item.pkl_dir}/${item.pkl_name}`;
                                        copyToClipboard(fullPath);
                                    }}
                                    title="复制文件路径"
                                    style={{ flexShrink: 0 }}
                                />
                            </div>
                        </List.Item>
                    )}
                />

                <div className="pkl-pagination">
                    <Pagination
                        current={pklPagination.current}
                        pageSize={pklPagination.pageSize}
                        total={pklPagination.total}
                        onChange={handlePklPageChange}
                        showSizeChanger={false}
                        size="small"
                        simple
                    />
                </div>
            </ResizableSider>

            {/* 中间图表展示区域 */}
            <div 
                className="visualization-content"
                style={{ 
                    flex: 1,
                    background: '#fff',
                    borderRight: '1px solid #f0f0f0',
                    overflow: 'hidden',
                    padding: '16px'
                }}
            >
                {selectedPkl ? (
                    <div style={{ width: '100%', height: '100%' }}>
                        {/* 显示最新的标注信息 */}
                        {latestAnnotationInfo && (
                            <div style={{ 
                                padding: '8px', 
                                background: '#f6ffed', 
                                borderBottom: '1px solid #f0f0f0',
                                fontSize: '12px',
                                marginBottom: '16px'
                            }}>
                                <Text strong>最新标注信息：</Text>
                                <Text> 标注员 {latestAnnotationInfo.employee_id} </Text>
                                <Text>于 {new Date(latestAnnotationInfo.updated_at).toLocaleString('zh-CN')} 更新</Text>
                            </div>
                        )}
                        
                        {loading.trajs ? (
                            <div style={{ 
                                display: 'flex', 
                                justifyContent: 'center', 
                                alignItems: 'center', 
                                height: '50%' 
                            }}>
                                <Spin tip="加载DLP轨迹数据..." />
                            </div>
                        ) : dlpTrajs.length === 0 ? (
                            <div style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: '50%',
                                color: '#999'
                            }}>
                                <Text>未找到DLP轨迹数据</Text>
                            </div>
                        ) : (
                            <div style={{ 
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '16px'
                            }}>
                                {/* 显示当前高亮的轨迹 */}
                                {highlightTrajIndex !== null && dlpTrajs[highlightTrajIndex] && (
                                    <>
                                        <div style={{
                                            padding: '8px 12px',
                                            background: '#e6f7ff',
                                            borderRadius: '4px',
                                            fontSize: '14px',
                                            fontWeight: 'bold',
                                            textAlign: 'center'
                                        }}>
                                            轨迹 {highlightTrajIndex + 1} - 概率: {dlpTrajs[highlightTrajIndex].probability.toFixed(3)}
                                        </div>
                                        
                                        {/* 三个图表：vel, acc, s */}
                                        <div style={{ 
                                            display: 'grid', 
                                            gridTemplateColumns: '1fr 1fr 1fr',
                                            gap: '16px',
                                            height: 'calc(100% - 60px)'
                                        }}>
                                            {/* Velocity 图表 */}
                                            <div style={{ height: '100%' }}>
                                                <ReactECharts
                                                    option={getChartOption(
                                                        dlpTrajs[highlightTrajIndex].vel, 
                                                        'Velocity', 
                                                        '#1890ff',
                                                        '速度 (m/s)'
                                                    )}
                                                    style={{ height: '100%', width: '100%' }}
                                                />
                                            </div>
                                            
                                            {/* Acceleration 图表 */}
                                            <div style={{ height: '100%' }}>
                                                <ReactECharts
                                                    option={getChartOption(
                                                        dlpTrajs[highlightTrajIndex].acc, 
                                                        'Acceleration', 
                                                        '#52c41a',
                                                        '加速度 (m/s²)'
                                                    )}
                                                    style={{ height: '100%', width: '100%' }}
                                                />
                                            </div>
                                            
                                            {/* S (Position) 图表 */}
                                            <div style={{ height: '100%' }}>
                                                <ReactECharts
                                                    option={getChartOption(
                                                        dlpTrajs[highlightTrajIndex].s, 
                                                        'Position', 
                                                        '#fa8c16',
                                                        '位置 (m)'
                                                    )}
                                                    style={{ height: '100%', width: '100%' }}
                                                />
                                            </div>
                                        </div>
                                    </>
                                )}
                                
                                {/* 如果没有选中轨迹，显示提示 */}
                                {highlightTrajIndex === null && (
                                    <div style={{
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        height: '100%',
                                        color: '#999',
                                        fontSize: '16px'
                                    }}>
                                        <Text>请从右侧选择一条轨迹以查看详细图表</Text>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                ) : (
                    <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                        color: '#999'
                    }}>
                        <Text>请从左侧选择一个PKL文件</Text>
                    </div>
                )}
            </div>

            {/* 右侧标注面板 - 可调整宽度 */}
            <ResizableSider
                width={rightSiderWidth}
                minWidth={250}
                maxWidth={500}
                onResize={setRightSiderWidth}
                position="right"
                className="annotation-sider"
            >
                <div className="annotation-panel">
                    <div className="annotation-header">
                        {selectedPkl && (
                            <div className="bad-data-button-container">
                                <Button
                                    danger={!isDirtyData}
                                    type={isDirtyData ? "default" : "primary"}
                                    icon={<WarningOutlined />}
                                    onClick={handleMarkAsDirty}
                                    loading={loading.markDirty}
                                >
                                    {isDirtyData ? "取消脏数据标记" : "标记为脏数据"}
                                </Button>
                            </div>
                        )}
                        {selectedPkl && !loading.trajs && dlpTrajs.length > 0 && (
                            <div className="annotation-stats">
                                <div className="stats-summary">
                                    <Tag color="blue" icon={<QuestionCircleOutlined />}>类型0: {annotationStats.type0}</Tag>
                                    <Tag color="cyan" icon={<QuestionCircleOutlined />}>类型1: {annotationStats.type1}</Tag>
                                    <Tag color="green" icon={<CheckCircleOutlined />}>类型2: {annotationStats.type2}</Tag>
                                    <Tag color="orange" icon={<QuestionCircleOutlined />}>类型3: {annotationStats.type3}</Tag>
                                    <Tag color="red" icon={<CloseCircleOutlined />}>类型4: {annotationStats.type4}</Tag>
                                    <Tag color="purple" icon={<QuestionCircleOutlined />}>类型5: {annotationStats.type5}</Tag>
                                </div>
                            </div>
                        )}
                    </div>

                    {!selectedPkl ? (
                        <div className="empty-annotation">
                            <Text>请先选择一个PKL文件</Text>
                        </div>
                    ) : loading.trajs ? (
                        <div className="loading-paths">
                            <Spin tip="加载轨迹数据..." />
                        </div>
                    ) : dlpTrajs.length === 0 ? (
                        <div className="no-paths">
                            <Text>未找到DLP轨迹数据</Text>
                        </div>
                    ) : (
                        <div className="paths-list">
                            {dlpTrajs.map((traj) => (
                                <div className="path-card-container" key={traj.index}>
                                    <Card
                                        className={highlightTrajIndex === traj.index ? 'path-card highlighted' : 'path-card'}
                                        size="small"
                                        onClick={() => handleHighlightTraj(traj.index)}
                                        hoverable
                                    >
                                        <div className="path-info">
                                            <div className="path-title">
                                                <Tag color="blue">轨迹 {traj.index + 1}</Tag>
                                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                                    概率: {traj.probability.toFixed(3)}
                                                </Text>
                                            </div>
                                        </div>
                                    </Card>

                                    <div className="path-actions" onClick={e => e.stopPropagation()}>
                                        <Radio.Group
                                            value={traj.annotation?.annotation}
                                            onChange={e => {
                                                e.stopPropagation();
                                                handleAnnotate(traj.index, e.target.value);
                                            }}
                                            buttonStyle="solid"
                                            style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}
                                        >
                                            <Radio.Button
                                                value="0"
                                                className={traj.annotation?.annotation === '0' ? 'type0-selected' : ''}
                                                style={{ marginBottom: '2px' }}
                                            >
                                                类型0
                                            </Radio.Button>
                                            <Radio.Button
                                                value="1"
                                                className={traj.annotation?.annotation === '1' ? 'type1-selected' : ''}
                                                style={{ marginBottom: '2px' }}
                                            >
                                                类型1
                                            </Radio.Button>
                                            <Radio.Button
                                                value="2"
                                                className={traj.annotation?.annotation === '2' ? 'type2-selected' : ''}
                                                style={{ marginBottom: '2px' }}
                                            >
                                                类型2
                                            </Radio.Button>
                                            <Radio.Button
                                                value="3"
                                                className={traj.annotation?.annotation === '3' ? 'type3-selected' : ''}
                                                style={{ marginBottom: '2px' }}
                                            >
                                                类型3
                                            </Radio.Button>
                                            <Radio.Button
                                                value="4"
                                                className={traj.annotation?.annotation === '4' ? 'type4-selected' : ''}
                                                style={{ marginBottom: '2px' }}
                                            >
                                                类型4
                                            </Radio.Button>
                                            <Radio.Button
                                                value="5"
                                                className={traj.annotation?.annotation === '5' ? 'type5-selected' : ''}
                                            >
                                                类型5
                                            </Radio.Button>
                                        </Radio.Group>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </ResizableSider>
        </div>
    );
};

export default DlpPathAnnotation;
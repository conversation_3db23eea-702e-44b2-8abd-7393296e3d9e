import React, { useState, useEffect } from 'react';
import { Layout, List, Card, Button, Input, Pagination, Tag, Space, message, Spin, Radio, Typography, Row, Col } from 'antd';
import { CopyOutlined, SearchOutlined, ExportOutlined, CheckOutlined, CheckCircleOutlined, CloseCircleOutlined, QuestionCircleOutlined, WarningOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import PickleVisualizer from '../components/PickleVisualizer';
import ResizableSider from '../components/ResizableSider';
import ImageWithPaths from '../components/ImageWithPaths';
import './PdpPathAnnotation.css'; // 复用相同的样式
import { EvaluationCase, EvaluationSet } from '../types';
import { useAuth } from '../contexts/AuthContext';

const { Sider, Content } = Layout;
const { Title, Text } = Typography;

interface LaneSceneAnnotation {
    vrulane_annotation?: string;
    frontlane_annotation?: string;
    employee_id?: string;
    created_at?: string;
    updated_at?: string;
}

interface AnnotationStats {
    total: number;
    annotated: number;
    vrulane_good: number;
    vrulane_bad: number;
    vrulane_ignore: number;
    frontlane_good: number;
    frontlane_bad: number;
    frontlane_ignore: number;
}

const copyToClipboard = async (text: string) => {
    try {
        await navigator.clipboard.writeText(text);
        message.success('已复制到剪贴板');
    } catch (err) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        message.success('已复制到剪贴板');
    }
};

const PdpLaneSceneAnnotation: React.FC = () => {
    const { id } = useParams<{ id: string }>();
    const { user, isAuthenticated } = useAuth();

    // 状态管理
    const [leftSiderWidth, setLeftSiderWidth] = useState(400);
    const [rightSiderWidth, setRightSiderWidth] = useState(350);
    
    const [evaluationSet, setEvaluationSet] = useState<EvaluationSet | null>(null);
    const [pklList, setPklList] = useState<EvaluationCase[]>([]);
    const [selectedPkl, setSelectedPkl] = useState<EvaluationCase | null>(null);
    const [currentAnnotation, setCurrentAnnotation] = useState<LaneSceneAnnotation | null>(null);
    const [imageData, setImageData] = useState<string | null>(null);
    const [ego2img, setEgo2img] = useState<number[][] | null>(null);
    const [egoYaw, setEgoYaw] = useState<number | null>(null);
    const [egoPathData, setEgoPathData] = useState<number[][]>([]); // 用于存储ego路径点数据
    const [loading, setLoading] = useState({
        pklList: false,
        annotation: false,
        data: false,
        markDirty: false,
        checkPkl: false,
    });

    const [pklPagination, setPklPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0
    });

    const [searchKeyword, setSearchKeyword] = useState('');
    const [isDirtyData, setIsDirtyData] = useState(false);
    const [showFullyAnnotatedOnly, setShowFullyAnnotatedOnly] = useState(false);
    const [checkStatusFilter, setCheckStatusFilter] = useState<'all' | 'checked' | 'unchecked'>('all');
    
    const [bagName, setBagName] = useState('');
    const [timeNs, setTimeNs] = useState('');
    const [timeRange, setTimeRange] = useState('');

    const [annotationStats, setAnnotationStats] = useState<AnnotationStats>({
        total: 0,
        annotated: 0,
        vrulane_good: 0,
        vrulane_bad: 0,
        vrulane_ignore: 0,
        frontlane_good: 0,
        frontlane_bad: 0,
        frontlane_ignore: 0,
    });

    useEffect(() => {
        if (!isAuthenticated) {
            message.error('请先登录后再进行标注操作');
            return;
        }
    }, [isAuthenticated]);

    // 加载PKL文件列表
    const loadPklList = async (
        page = pklPagination.current, 
        pageSize = pklPagination.pageSize, 
        search = searchKeyword, 
        fullyAnnotatedOnly = showFullyAnnotatedOnly, 
        checkStatus = checkStatusFilter,
        bag_name = bagName,
        time_ns = timeNs,
        time_range = timeRange
    ) => {
        if (!id) return;

        setLoading(prev => ({ ...prev, pklList: true }));
        try {
            const params: any = {
                page,
                per_page: pageSize,
                search,
                annotated_filter: fullyAnnotatedOnly, // 使用车道场景标注过滤
                check_status: checkStatus
            };

            if (bag_name && time_ns && time_range) {
                params.bag_name = bag_name;
                params.time_ns = time_ns;
                params.time_range = time_range;
            }

            const response = await axios.get(`/api/evaluation_sets/${id}`, { params });

            if (response.data.success) {
                const evaluationSetData: EvaluationSet = {
                    id: response.data.evaluation_set.id,
                    set_name: response.data.evaluation_set.name,
                    creator_name: response.data.evaluation_set.creator_name || '',
                    description: response.data.evaluation_set.description,
                    cases: response.data.cases || [],
                    case_count: response.data.case_count || 0,
                    created_at: response.data.evaluation_set.created_at
                };
                setEvaluationSet(evaluationSetData);
                setPklList(response.data.cases || []);
                setPklPagination({
                    current: response.data.page || page,
                    pageSize: response.data.per_page || pageSize,
                    total: response.data.case_count || 0
                });
            } else {
                message.error(response.data.error || '加载PKL列表失败');
            }
        } catch (error) {
            console.error('Failed to load PKL list:', error);
            message.error('加载PKL列表出错');
        } finally {
            setLoading(prev => ({ ...prev, pklList: false }));
        }
    };

    // 选择PKL文件时加载数据
    const handlePklSelect = async (pkl: EvaluationCase) => {
        setSelectedPkl(pkl);
        setCurrentAnnotation(null);
        setImageData(null);
        setLoading(prev => ({ ...prev, data: true }));

        try {
            const response = await axios.get(`/api/annotation/lane-scene/pkl/${pkl.id}`, {
                params: {
                    evaluation_set_id: id
                }
            });

            if (response.data.success) {
                setCurrentAnnotation(response.data.current_annotation);
                setIsDirtyData(response.data.pkl_info.is_dirty || false);

                if (response.data.image_data) {
                    setImageData(response.data.image_data);
                }

                if (response.data.ego2img) {
                    setEgo2img(response.data.ego2img);
                } else {
                    setEgo2img(null);
                }

                if (response.data.ego_yaw !== null) {
                    setEgoYaw(response.data.ego_yaw);
                } else {
                    setEgoYaw(null);
                }
                if (response.data.ego_path_data) {
                    setEgoPathData(response.data.ego_path_data);
                } else {
                    setEgoPathData([]);
                }

            } else {
                message.error('加载车道场景数据失败');
            }
        } catch (error) {
            console.error('Failed to load lane scene data:', error);
            message.error('加载车道场景数据出错');
        } finally {
            setLoading(prev => ({ ...prev, data: false }));
        }
    };

    // 处理标注
    const handleAnnotate = async (annotationType: 'vrulane' | 'frontlane', annotation: string) => {
        if (!selectedPkl) {
            message.error('请先选择PKL文件');
            return;
        }

        // 检查是否需要删除标注
        const currentValue = annotationType === 'vrulane' 
            ? currentAnnotation?.vrulane_annotation 
            : currentAnnotation?.frontlane_annotation;
        const isDeleteAction = currentValue === annotation;

        setLoading(prev => ({ ...prev, annotation: true }));
        try {
            const requestData: any = {
                pkl_id: selectedPkl.id,
                evaluation_set_id: parseInt(id || '0'),
                delete_annotation: isDeleteAction
            };

            if (isDeleteAction) {
                requestData.annotation_type = annotationType;
            } else {
                if (annotationType === 'vrulane') {
                    requestData.vrulane_annotation = annotation;
                } else {
                    requestData.frontlane_annotation = annotation;
                }
            }

            const response = await axios.post('/api/annotation/lane-scene/annotate', requestData);

            if (response.data.success) {
                // 更新当前标注状态
                const updatedAnnotation = { ...currentAnnotation };
                if (isDeleteAction) {
                    if (annotationType === 'vrulane') {
                        updatedAnnotation.vrulane_annotation = undefined;
                    } else {
                        updatedAnnotation.frontlane_annotation = undefined;
                    }
                    message.success('标注已删除');
                } else {
                    if (annotationType === 'vrulane') {
                        updatedAnnotation.vrulane_annotation = annotation;
                    } else {
                        updatedAnnotation.frontlane_annotation = annotation;
                    }
                    updatedAnnotation.employee_id = user?.employee_id;
                    updatedAnnotation.updated_at = new Date().toISOString();
                    message.success('标注保存成功');
                }

                setCurrentAnnotation(updatedAnnotation);
            } else {
                message.error('操作失败');
            }
        } catch (error) {
            console.error('Failed to save annotation:', error);
            message.error('操作出错');
        } finally {
            setLoading(prev => ({ ...prev, annotation: false }));
        }
    };

    // 处理标记为脏数据
    const handleMarkAsDirty = async () => {
        if (!selectedPkl) {
            message.error('请先选择PKL文件');
            return;
        }

        setLoading(prev => ({ ...prev, markDirty: true }));
        try {
            const response = await axios.post('/api/annotation/mark-dirty', {
                pkl_id: selectedPkl.id,
                is_dirty: !isDirtyData
            });

            if (response.data.success) {
                setIsDirtyData(response.data.is_dirty);
                message.success(
                    response.data.is_dirty
                        ? '已标记为脏数据'
                        : '已取消脏数据标记'
                );

                const updatedPklList = pklList.map(pkl => {
                    if (pkl.id === selectedPkl.id) {
                        return { ...pkl, dirty_data: response.data.is_dirty };
                    }
                    return pkl;
                });
                setPklList(updatedPklList);
            } else {
                message.error('操作失败');
            }
        } catch (error) {
            console.error('Failed to mark as dirty data:', error);
            message.error('操作出错');
        } finally {
            setLoading(prev => ({ ...prev, markDirty: false }));
        }
    };

    // 处理PKL检查状态切换
    const handleTogglePklCheck = async (pkl: EvaluationCase, event: React.MouseEvent) => {
        event.stopPropagation();

        if (!id) {
            message.error('评测集ID无效');
            return;
        }

        setLoading(prev => ({ ...prev, checkPkl: true }));

        try {
            const isCurrentlyChecked = pkl.is_checked;
            const endpoint = isCurrentlyChecked
                ? `/api/evaluation_sets/${id}/check-pkl/${pkl.id}`
                : `/api/evaluation_sets/${id}/check-pkl/${pkl.id}`;

            const method = isCurrentlyChecked ? 'delete' : 'post';
            const response = await axios[method](endpoint);

            if (response.data.success) {
                const updatedPklList = pklList.map(item => {
                    if (item.id === pkl.id) {
                        return {
                            ...item,
                            is_checked: !isCurrentlyChecked,
                            checked_at: isCurrentlyChecked ? undefined : new Date().toISOString(),
                            checked_by: isCurrentlyChecked ? undefined : user?.employee_id
                        };
                    }
                    return item;
                });

                setPklList(updatedPklList);

                if (selectedPkl?.id === pkl.id) {
                    setSelectedPkl({
                        ...selectedPkl,
                        is_checked: !isCurrentlyChecked,
                        checked_at: isCurrentlyChecked ? undefined : new Date().toISOString(),
                        checked_by: isCurrentlyChecked ? undefined : user?.employee_id
                    });
                }

                message.success(
                    isCurrentlyChecked ? 'PKL检查标记已取消' : 'PKL已标记为已检查'
                );
            } else {
                message.error('操作失败：' + (response.data.error || '未知错误'));
            }
        } catch (error) {
            console.error('Failed to toggle PKL check status:', error);
            message.error('操作出错，请稍后再试');
        } finally {
            setLoading(prev => ({ ...prev, checkPkl: false }));
        }
    };

    // 导出标注数据
    const handleExportAnnotations = async () => {
        if (!id) {
            message.error('评测集ID无效');
            return;
        }

        try {
            const exportUrl = `/api/annotation/lane-scene/export/${id}`;
            window.open(exportUrl, '_blank');
        } catch (error) {
            console.error('Failed to export annotations:', error);
            message.error('导出出错，请稍后再试');
        }
    };

    // 处理分页变化
    const handlePklPageChange = (page: number, pageSize?: number) => {
        loadPklList(page, pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, bagName, timeNs, timeRange);
    };

    // 处理搜索
    const handleSearch = () => {
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, bagName, timeNs, timeRange);
    };
    
    const handleFilterToggle = () => {
        const newFilterState = !showFullyAnnotatedOnly;
        setShowFullyAnnotatedOnly(newFilterState);
        loadPklList(1, pklPagination.pageSize, searchKeyword, newFilterState, checkStatusFilter, bagName, timeNs, timeRange);
    };
    
    const handleCheckStatusFilter = (status: 'all' | 'checked' | 'unchecked') => {
        setCheckStatusFilter(status);
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, status, bagName, timeNs, timeRange);
    };

    const handleClearBagTimeFilter = () => {
        setBagName('');
        setTimeNs('');
        setTimeRange('');
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, '', '', '');
    };

    // 初始加载
    useEffect(() => {
        if (id) {
            loadPklList();
        }
    }, [id]);

    return (
        <div className="pdp-path-annotation-layout" style={{ display: 'flex', height: '100vh' }}>
            {/* 左侧PKL列表 */}
            <ResizableSider
                width={leftSiderWidth}
                minWidth={200}
                maxWidth={600}
                onResize={setLeftSiderWidth}
                position="left"
                className="pkl-list-sider"
            >
                <div className="pkl-list-header">
                    <Space direction="vertical" style={{ width: '100%' }} size="small">
                        {evaluationSet && (
                            <div style={{ 
                                marginBottom: '4px', 
                                padding: '4px 6px', 
                                background: '#f0f0f0', 
                                borderRadius: '3px',
                                border: '1px solid #d9d9d9'
                            }}>
                                <Text strong style={{ fontSize: '12px', color: '#1890ff', lineHeight: '1.2' }}>
                                    {evaluationSet.set_name}
                                </Text>
                            </div>
                        )}
                        
                        <Input
                            placeholder="搜索PKL文件"
                            prefix={<SearchOutlined />}
                            value={searchKeyword}
                            onChange={e => setSearchKeyword(e.target.value)}
                            onPressEnter={handleSearch}
                            size="small"
                            style={{ marginBottom: '4px' }}
                        />
                        
                        <div style={{ border: '1px solid #d9d9d9', padding: '6px', borderRadius: '4px' }}>
                            <Text strong style={{ fontSize: '11px', color: '#666' }}>bag_name时间戳过滤:</Text>
                            <Input
                                placeholder="bag_name"
                                value={bagName}
                                onChange={e => setBagName(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Input
                                placeholder="时间戳(纳秒)"
                                value={timeNs}
                                onChange={e => setTimeNs(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Input
                                placeholder="时间范围(秒)"
                                value={timeRange}
                                onChange={e => setTimeRange(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Space size="small" style={{ marginTop: '3px', width: '100%' }}>
                                <Button
                                    type="primary"
                                    size="small"
                                    onClick={handleSearch}
                                    style={{ flex: 1 }}
                                >
                                    应用过滤
                                </Button>
                                <Button
                                    size="small"
                                    onClick={handleClearBagTimeFilter}
                                    style={{ flex: 1 }}
                                >
                                    清空
                                </Button>
                            </Space>
                        </div>

                        <Space size="small" style={{ marginTop: '4px' }}>
                            <Button
                                type={showFullyAnnotatedOnly ? "primary" : "default"}
                                icon={<CheckCircleOutlined />}
                                onClick={handleFilterToggle}
                                size="small"
                            >
                                {showFullyAnnotatedOnly ? "显示全部" : "仅显示已标注"}
                            </Button>
                            <Radio.Group
                                value={checkStatusFilter}
                                onChange={e => handleCheckStatusFilter(e.target.value)}
                                size="small"
                                buttonStyle="solid"
                            >
                                <Radio.Button value="all">全部</Radio.Button>
                                <Radio.Button value="checked">已检查</Radio.Button>
                                <Radio.Button value="unchecked">未检查</Radio.Button>
                            </Radio.Group>
                        </Space>
                        <Button
                            type="default"
                            icon={<ExportOutlined />}
                            onClick={handleExportAnnotations}
                            size="small"
                            style={{ width: '100%', marginTop: '4px' }}
                        >
                            导出车道场景标注数据
                        </Button>
                    </Space>
                </div>

                <List
                    loading={loading.pklList}
                    dataSource={pklList}
                    renderItem={item => (
                        <List.Item
                            className={selectedPkl?.id === item.id ? 'pkl-item selected' : 'pkl-item'}
                            onClick={() => handlePklSelect(item)}
                            style={{ display: 'flex', alignItems: 'flex-start' }}
                        >
                            <div className="pkl-item-content" style={{ flex: 1, minWidth: 0 }}>
                                <div className="pkl-name" style={{
                                    fontSize: '11px',
                                    lineHeight: '1.3',
                                    wordBreak: 'break-all',
                                    whiteSpace: 'normal',
                                    maxWidth: '100%',
                                    marginBottom: '4px'
                                }}>
                                    {item.pkl_name}
                                    {item.dirty_data && (
                                        <Tag color="red" style={{ marginLeft: 5, fontSize: '10px' }}>
                                            <WarningOutlined /> 脏数据
                                        </Tag>
                                    )}
                                    {item.is_checked && (
                                        <Tag color="green" style={{ marginLeft: 5, fontSize: '10px' }}>
                                            <CheckOutlined /> 已检查
                                        </Tag>
                                    )}
                                </div>
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'row', gap: 4, marginLeft: 8 }}>
                                <Button
                                    type={item.is_checked ? "primary" : "default"}
                                    icon={<CheckOutlined />}
                                    size="small"
                                    loading={loading.checkPkl}
                                    onClick={(e) => handleTogglePklCheck(item, e)}
                                    title={item.is_checked ? "取消检查标记" : "标记为已检查"}
                                    style={{
                                        flexShrink: 0,
                                        backgroundColor: item.is_checked ? '#52c41a' : undefined,
                                        borderColor: item.is_checked ? '#52c41a' : undefined,
                                    }}
                                />
                                <Button
                                    type="text"
                                    icon={<CopyOutlined />}
                                    size="small"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        const fullPath = `${item.pkl_dir}/${item.pkl_name}`;
                                        copyToClipboard(fullPath);
                                    }}
                                    title="复制文件路径"
                                    style={{ flexShrink: 0 }}
                                />
                            </div>
                        </List.Item>
                    )}
                />

                <div className="pkl-pagination">
                    <Pagination
                        current={pklPagination.current}
                        pageSize={pklPagination.pageSize}
                        total={pklPagination.total}
                        onChange={handlePklPageChange}
                        showSizeChanger={false}
                        size="small"
                        simple
                    />
                </div>
            </ResizableSider>

            {/* 中间可视化区域 */}
            <div 
                className="visualization-content"
                style={{ 
                    flex: 1,
                    background: '#fff',
                    borderRight: '1px solid #f0f0f0',
                    overflow: 'hidden'
                }}
            >
                {selectedPkl ? (
                    <div className="visualization-container" style={{ width: '100%', height: '100%' }}>
                        <div className="visualization-area" style={{ width: '100%', height: '100%' }}>
                            {loading.data ? (
                                <div style={{ 
                                    display: 'flex', 
                                    justifyContent: 'center', 
                                    alignItems: 'center', 
                                    height: '100%' 
                                }}>
                                    <Spin tip="加载可视化..." />
                                </div>
                            ) : (
                                <div style={{ width: '100%', height: '100%' }}>
                                    {imageData && (
                                        <div className="e2e-image-container" style={{ 
                                            width: '100%', 
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            marginBottom: '10px'
                                        }}>
                                            <ImageWithPaths
                                                imageData={imageData}
                                                pdpPaths={{}} // 车道场景标注不需要路径显示
                                                highlightPathIndex={null}
                                                ego2img={ego2img}
                                                egoYaw={egoYaw}
                                                egoPathPoints={egoPathData}  // 传入 ego 路径点
                                                showEgoPath={true}           // 启用 ego 路径显示
                                                egoPathStyle={{
                                                    color: '#ff0000',        // 红色线条（修改）
                                                    lineWidth: 3,            // 线宽
                                                    pointRadius: 2,          // 点半径（虽然不显示点，但保留配置）
                                                    pointColor: '#ff4d4f',   // 红色点（不显示）
                                                    showPoints: false,       // 不显示点（修改）
                                                    showLine: true           // 显示连线
                                                }}
                                                style={{ 
                                                    maxWidth: '100%',
                                                    maxHeight: '200px'
                                                }}
                                            />
                                        </div>
                                    )}
                                    <div style={{ 
                                        width: '100%', 
                                        height: imageData ? 'calc(100% - 220px)' : '100%',
                                        minHeight: '400px'
                                    }}>
                                        <PickleVisualizer
                                            evaluationCase={selectedPkl}
                                            highlightPathIndex={null}
                                            pdpPaths={{}}
                                            height={imageData ? 'calc(100vh - 320px)' : '60vh'}
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ) : (
                    <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                        color: '#999'
                    }}>
                        <Text>请从左侧选择一个PKL文件</Text>
                    </div>
                )}
            </div>

            {/* 右侧车道场景标注面板 */}
            <ResizableSider
                width={rightSiderWidth}
                minWidth={300}
                maxWidth={500}
                onResize={setRightSiderWidth}
                position="right"
                className="annotation-sider"
            >
                <div className="annotation-panel">
                    <div className="annotation-header">
                        {selectedPkl && (
                            <div className="bad-data-button-container">
                                <Button
                                    danger={!isDirtyData}
                                    type={isDirtyData ? "default" : "primary"}
                                    icon={<WarningOutlined />}
                                    onClick={handleMarkAsDirty}
                                    loading={loading.markDirty}
                                >
                                    {isDirtyData ? "取消脏数据标记" : "标记为脏数据"}
                                </Button>
                            </div>
                        )}
                        
                        <Title level={5} style={{ margin: '16px 0 8px 0' }}>车道场景标注</Title>
                    </div>

                    {!selectedPkl ? (
                        <div className="empty-annotation">
                            <Text>请先选择一个PKL文件</Text>
                        </div>
                    ) : loading.data ? (
                        <div className="loading-paths">
                            <Spin tip="加载数据..." />
                        </div>
                    ) : (
                        <div className="lane-annotation-container">
                            {/* 非机动车道标注 */}
                            <Card
                                title="非机动车道评价"
                                size="small"
                                style={{ marginBottom: '16px' }}
                                extra={
                                    currentAnnotation?.vrulane_annotation && (
                                        <Tag color={
                                            currentAnnotation.vrulane_annotation === 'good' ? 'green' : 
                                            currentAnnotation.vrulane_annotation === 'bad' ? 'red' : 'default'
                                        }>
                                            {currentAnnotation.vrulane_annotation === 'good' ? '好' :
                                             currentAnnotation.vrulane_annotation === 'bad' ? '差' : '忽略'}
                                        </Tag>
                                    )
                                }
                            >
                                <Radio.Group
                                    value={currentAnnotation?.vrulane_annotation}
                                    onChange={e => {
                                        handleAnnotate('vrulane', e.target.value);
                                    }}
                                    buttonStyle="solid"
                                    style={{ width: '100%' }}
                                >
                                    <Row gutter={[8, 8]}>
                                        <Col span={8}>
                                            <Radio.Button
                                                value="good"
                                                style={{ width: '100%', textAlign: 'center' }}
                                                className={currentAnnotation?.vrulane_annotation === 'good' ? 'good-selected' : ''}
                                            >
                                                <CheckCircleOutlined /> 好
                                            </Radio.Button>
                                        </Col>
                                        <Col span={8}>
                                            <Radio.Button
                                                value="bad"
                                                style={{ width: '100%', textAlign: 'center' }}
                                                className={currentAnnotation?.vrulane_annotation === 'bad' ? 'bad-selected' : ''}
                                            >
                                                <CloseCircleOutlined /> 差
                                            </Radio.Button>
                                        </Col>
                                        <Col span={8}>
                                            <Radio.Button
                                                value="ignore"
                                                style={{ width: '100%', textAlign: 'center' }}
                                                className={currentAnnotation?.vrulane_annotation === 'ignore' ? 'ignore-selected' : ''}
                                            >
                                                <EyeInvisibleOutlined /> 忽略
                                            </Radio.Button>
                                        </Col>
                                    </Row>
                                </Radio.Group>
                            </Card>

                            {/* 推荐车道标注 */}
                            <Card
                                title="推荐车道评价"
                                size="small"
                                extra={
                                    currentAnnotation?.frontlane_annotation && (
                                        <Tag color={
                                            currentAnnotation.frontlane_annotation === 'good' ? 'green' : 
                                            currentAnnotation.frontlane_annotation === 'bad' ? 'red' : 'default'
                                        }>
                                            {currentAnnotation.frontlane_annotation === 'good' ? '好' :
                                             currentAnnotation.frontlane_annotation === 'bad' ? '差' : '忽略'}
                                        </Tag>
                                    )
                                }
                            >
                                <Radio.Group
                                    value={currentAnnotation?.frontlane_annotation}
                                    onChange={e => {
                                        handleAnnotate('frontlane', e.target.value);
                                    }}
                                    buttonStyle="solid"
                                    style={{ width: '100%' }}
                                >
                                    <Row gutter={[8, 8]}>
                                        <Col span={8}>
                                            <Radio.Button
                                                value="good"
                                                style={{ width: '100%', textAlign: 'center' }}
                                                className={currentAnnotation?.frontlane_annotation === 'good' ? 'good-selected' : ''}
                                            >
                                                <CheckCircleOutlined /> 好
                                            </Radio.Button>
                                        </Col>
                                        <Col span={8}>
                                            <Radio.Button
                                                value="bad"
                                                style={{ width: '100%', textAlign: 'center' }}
                                                className={currentAnnotation?.frontlane_annotation === 'bad' ? 'bad-selected' : ''}
                                            >
                                                <CloseCircleOutlined /> 差
                                            </Radio.Button>
                                        </Col>
                                        <Col span={8}>
                                            <Radio.Button
                                                value="ignore"
                                                style={{ width: '100%', textAlign: 'center' }}
                                                className={currentAnnotation?.frontlane_annotation === 'ignore' ? 'ignore-selected' : ''}
                                            >
                                                <EyeInvisibleOutlined /> 忽略
                                            </Radio.Button>
                                        </Col>
                                    </Row>
                                </Radio.Group>
                            </Card>

                            {/* 标注信息 */}
                            {currentAnnotation && (currentAnnotation.vrulane_annotation || currentAnnotation.frontlane_annotation) && (
                                <Card title="标注信息" size="small" style={{ marginTop: '16px' }}>
                                    {currentAnnotation.employee_id && (
                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                            标注人: {currentAnnotation.employee_id}
                                        </Text>
                                    )}
                                    {currentAnnotation.updated_at && (
                                        <div>
                                            <Text style={{ fontSize: '12px', color: '#666' }}>
                                                更新时间: {new Date(currentAnnotation.updated_at).toLocaleString()}
                                            </Text>
                                        </div>
                                    )}
                                </Card>
                            )}
                        </div>
                    )}
                </div>
            </ResizableSider>
        </div>
    );
};

export default PdpLaneSceneAnnotation;
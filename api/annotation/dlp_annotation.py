from fastapi import APIRouter, HTTPException, Body, Path, Query, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime, date
import os
from database.db_operations import execute_query
from api.visualize.pickle_visualize import load_pickle, extract_geometry_from_pickle
from api.auth.auth import get_current_user
import cv2
import numpy as np
import base64
from api.visualize.calib_reader import CalibReader, CalibReaderDict
import json
from shapely.geometry import LineString, Point

calib_reader_dict = CalibReaderDict()
router = APIRouter(prefix="/api/annotation", tags=["dlp_annotation"])


def project_valid_trajectories_to_path(
    trajectories: np.ndarray, masks, path, valid_obj_length
):
    """
    对有效轨迹进行投影，只投影有效点
    :param trajectories: 有效轨迹 (B, N, 2)
    :param masks: 掩码 (B, N)
    :param path: 路径点 (M, 2)
    :param valid_obj_length: 有效物体长度 (B,)
    :return:有效轨迹点 (B, K, 3)  (B,K,(时间戳,投影后的轨迹点s值,速度)) 每条轨迹只包含有效点的投影
    """
    # 创建路径的LineString对象
    if trajectories.ndim == 2:
        trajectories = trajectories[np.newaxis, :]
        masks = masks[np.newaxis, :]
    path_line = LineString(path)

    projected_trajectories = []
    valid_points_count = []

    for trajectory, mask, length in zip(trajectories, masks, valid_obj_length):
        # 只处理有效点
        valid_points = trajectory[mask.astype(bool)]
        valid_indices = np.where(mask)[0]  # 获取有效点的索引
        # 投影有效点并生成时间戳
        projected_points_with_time = []
        previous_s = 0
        previous_timestamp = 0
        for i, (point, original_index) in enumerate(zip(valid_points, valid_indices)):
            timestamp = (original_index + 1) * 0.5
            if timestamp > 4:
                break
            point_geom = Point(point)
            # 计算投影点在路径上的s值
            projected_s = path_line.project(point_geom)

            # 计算时间戳（基于原始索引，每个点间隔0.5秒）
            # 如果中间有无效点，时间间隔会相应增加
            # caculate velocity acoording to s_value and timestamp
            if i == 0:
                velocity = 0
            else:
                velocity = (projected_s - previous_s) / (timestamp - previous_timestamp)
            previous_s = projected_s
            previous_timestamp = timestamp
            offset_s = projected_s - 3.5 - length / 2  # 减去自车长度和他车半车长
            # 添加 [timestamp, s_value] 到结果中
            projected_points_with_time.append([timestamp, offset_s, velocity])
        projected_points_with_time[0][2] = projected_points_with_time[1][
            2
        ]  # padd first point velocity
        projected_trajectories.append(np.array(projected_points_with_time))
        valid_points_count.append(len(projected_points_with_time))

    return projected_trajectories, valid_points_count


class EvaluationSetProgress(BaseModel):
    evaluation_set_id: int
    evaluation_set_name: str
    total_pkls: int
    annotated_pkls: int
    progress_percentage: float
    last_updated: Optional[datetime] = None


class EmployeeProgress(BaseModel):
    employee_id: str
    annotation_date: date
    annotated_pkls_count: int


class AnnotationProgressResponse(BaseModel):
    success: bool
    evaluation_sets_progress: List[EvaluationSetProgress]
    employee_daily_progress: List[EmployeeProgress]


class DlpPathAnnotation(BaseModel):
    pkl_id: int
    traj_index: int
    annotation: str  # "0/1/2/3/4/5"
    inference_config_id: Optional[int] = 3
    delete_annotation: Optional[bool] = False
    evaluation_set_id: Optional[int] = None


class MarkAsDirtyRequest(BaseModel):
    pkl_id: int
    is_dirty: bool = True


class DLPSelectedObjects(BaseModel):
    pkl_id: int
    selected_object_ids: List[str]
    delete_annotation: Optional[bool] = False
    evaluation_set_id: Optional[int] = None


# 新增标签保存请求模型
class SaveTagRequest(BaseModel):
    pkl_id: int
    tag: str
    evaluation_set_id: Optional[int] = None


@router.post("/mark-dirty")
async def mark_as_dirty_data(request: MarkAsDirtyRequest):
    """
    标记或取消标记PKL文件为脏数据
    """
    # 检查pkl_id是否存在
    check_query = "SELECT id FROM evaluation_case_pool WHERE id = %s"
    check_result = execute_query(check_query, (request.pkl_id,), fetch_one=True)

    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="指定的PKL ID不存在")

    # 更新脏数据标记
    update_query = """
    UPDATE evaluation_case_pool
    SET dirty_data = %s
    WHERE id = %s
    """
    params = (request.is_dirty, request.pkl_id)
    result = execute_query(update_query, params)

    if not result["success"]:
        raise HTTPException(
            status_code=500, detail=f"更新脏数据状态时出错: {result['error']}"
        )

    return {"success": True, "message": "数据状态已更新", "is_dirty": request.is_dirty}


@router.get("/dlp/{evaluation_set_id}/cases")
async def get_evaluation_set_cases(
    evaluation_set_id: int,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    vin: Optional[str] = Query(None, description="VIN码精确匹配"),
    time_ns: Optional[str] = Query(None, description="时间戳(纳秒)"),
    time_range: Optional[str] = Query(None, description="时间范围(秒)"),
    fully_annotated_only: bool = Query(False),
    check_status: str = Query("all"),
):
    """
    获取评测集中的PKL文件列表，支持VIN码和时间范围搜索
    """
    try:
        # 构建基础查询
        base_query = """
        SELECT DISTINCT ecp.id, ecp.pkl_dir, ecp.pkl_name, ecp.vehicle_type,
               ecp.vin, ecp.time_ns, ecp.key_obs_id, ecp.dirty_data,
               ecp.created_at, esc.is_checked
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case esc ON ecp.id = esc.case_id
        WHERE esc.evaluation_set_id = %s
        """

        count_query = """
        SELECT COUNT(DISTINCT ecp.id)
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case esc ON ecp.id = esc.case_id
        WHERE esc.evaluation_set_id = %s
        """

        params = [evaluation_set_id]

        # 添加VIN码搜索条件
        if vin and vin.strip():
            base_query += " AND ecp.vin = %s"
            count_query += " AND ecp.vin = %s"
            params.append(vin.strip())

        # 添加时间范围搜索条件
        if time_ns and time_range:
            try:
                time_ns_int = int(time_ns)
                time_range_int = int(time_range)
                time_range_ns = time_range_int * 1000000000  # 转换为纳秒

                start_time = time_ns_int - time_range_ns
                end_time = time_ns_int + time_range_ns

                base_query += " AND ecp.time_ns >= %s AND ecp.time_ns <= %s"
                count_query += " AND ecp.time_ns >= %s AND ecp.time_ns <= %s"
                params.extend([start_time, end_time])
            except ValueError:
                raise HTTPException(
                    status_code=400, detail="时间戳和时间范围必须为有效数字"
                )

        # 添加其他筛选条件
        if fully_annotated_only:
            # 这里需要根据您的标注表结构来调整
            base_query += """
            AND ecp.id IN (
                SELECT DISTINCT pkl_id FROM dlp_path_annotation
                WHERE evaluation_set_id = %s
                GROUP BY pkl_id
            )
            """
            count_query += """
            AND ecp.id IN (
                SELECT DISTINCT pkl_id FROM dlp_path_annotation
                WHERE evaluation_set_id = %s
                GROUP BY pkl_id
            )
            """
            params.append(evaluation_set_id)

        if check_status == "checked":
            base_query += " AND esc.is_checked = 1"
            count_query += " AND esc.is_checked = 1"
        elif check_status == "unchecked":
            base_query += " AND esc.is_checked = 0"
            count_query += " AND esc.is_checked = 0"

        # 添加排序和分页
        base_query += " ORDER BY ecp.created_at DESC LIMIT %s OFFSET %s"
        offset = (page - 1) * page_size
        params.extend([page_size, offset])

        # 执行查询
        cases_result = execute_query(base_query, params)
        count_result = execute_query(
            count_query, params[:-2], fetch_one=True
        )  # 去掉LIMIT和OFFSET参数

        if not cases_result["success"] or not count_result["success"]:
            raise HTTPException(status_code=500, detail="查询失败")

        # 格式化返回数据
        formatted_cases = []
        for case in cases_result["data"]:
            formatted_cases.append(
                {
                    "id": case[0],
                    "pkl_dir": case[1],
                    "pkl_name": case[2],
                    "vehicle_type": case[3],
                    "vin": case[4],
                    "time_ns": case[5],
                    "key_obs_id": case[6],
                    "dirty_data": bool(case[7]),
                    "created_at": case[8],
                    "is_checked": bool(case[9]),
                }
            )

        return {
            "success": True,
            "data": formatted_cases,
            "total": count_result["data"][0] if count_result["data"] else 0,
            "page": page,
            "page_size": page_size,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取评测集案例失败: {str(e)}")


@router.get("/dlp-paths/{pkl_id}")
async def get_dlp_paths(
    pkl_id: int,
    evaluation_set_id: Optional[int] = Query(
        None, description="评测集ID，用于过滤标注结果"
    ),
):
    """获取指定PKL文件中的所有DLP轨迹信息,并包含可视化所需的轨迹数据和e2e_image图像"""
    # 查询pkl文件路径
    query = """
    SELECT pkl_dir, pkl_name, dirty_data, vin FROM evaluation_case_pool WHERE id = %s
    """
    result = execute_query(query, (pkl_id,), fetch_one=True)

    if not result["success"] or not result["data"]:
        raise HTTPException(status_code=404, detail="找不到指定的PKL文件")

    pkl_dir, pkl_name, is_dirty, vin = result["data"]
    pickle_path = os.path.join(pkl_dir, pkl_name)

    # 检查文件是否存在
    if not os.path.exists(pickle_path):
        raise HTTPException(status_code=404, detail="PKL文件不存在")
    # try:
    if True:
        # 加载pickle文件
        data = load_pickle(pickle_path)
        ego_v = data["ego_input"][0] ** 2 + data["ego_input"][1] ** 2
        ego_v = ego_v**0.5
        ego_s = ego_v * 6
        ego_default_s = 40
        ego_final_s = max(ego_s, ego_default_s)
        ego_final_index = int(ego_final_s / 2)
        pdp_paths = data.get("pdp_path", [])
        pdp_trajs = data.get("pdp_traj", [])
        future_obj = data.get("Seach", [])
        future_obj_mask = data.get("Sweach", [])
        # np.ndarray(200,2)
        ego_gt = data.get("path_point", [])
        ego_gt_mask = data.get("path_mask", [])
        ego_traj = data.get("ego_label", None)[:20, ...]
        # 修改: 在ego_traj前补零点，然后累加每一段的距离
        ego_traj_padded = np.concatenate([np.zeros((1, 2)), ego_traj], axis=0)
        ego_traj_diff = np.diff(ego_traj_padded, axis=0)
        ego_traj_length = np.cumsum(np.linalg.norm(ego_traj_diff, axis=-1))
        ego_traj_vel = data.get("future_traj_velocity", None)

        # 查询现有标注
        if evaluation_set_id is not None:
            query = """
            SELECT traj_index, annotation
            FROM dlp_path_annotation
            WHERE pkl_id = %s AND evaluation_set_id = %s
            """
            annotations_result = execute_query(
                query, (pkl_id, evaluation_set_id), fetch_all=True
            )
        else:
            # 如果没有提供evaluation_set_id，使用原来的查询方式（为了向后兼容）
            query = """
            SELECT traj_index, annotation
            FROM dlp_path_annotation
            WHERE pkl_id = %s
            """
            annotations_result = execute_query(query, (pkl_id,), fetch_all=True)

        # 转换为字典以便快速查找
        annotations = {}
        if annotations_result["success"] and annotations_result["data"]:
            for traj_index, annotation in annotations_result["data"]:
                annotations[traj_index] = {"annotation": annotation}

        # 查询最新的标注更新信息
        latest_annotation_info = None
        if evaluation_set_id is not None:
            latest_query = """
            SELECT employee_id, updated_at
            FROM dlp_path_annotation
            WHERE pkl_id = %s AND evaluation_set_id = %s
            ORDER BY updated_at DESC
            LIMIT 1
            """
            latest_result = execute_query(
                latest_query, (pkl_id, evaluation_set_id), fetch_one=True
            )
            if latest_result["success"] and latest_result["data"]:
                latest_annotation_info = {
                    "employee_id": latest_result["data"][0],
                    "updated_at": latest_result["data"][1],
                }
        else:
            latest_query = """
            SELECT employee_id, updated_at
            FROM dlp_path_annotation
            WHERE pkl_id = %s
            ORDER BY updated_at DESC
            LIMIT 1
            """
            latest_result = execute_query(latest_query, (pkl_id,), fetch_one=True)
            if latest_result["success"] and latest_result["data"]:
                latest_annotation_info = {
                    "employee_id": latest_result["data"][0],
                    "updated_at": latest_result["data"][1],
                }

        # 查询标签信息
        if evaluation_set_id is not None:
            tag_query = """
            SELECT tag
            FROM dlp_tags
            WHERE pkl_id = %s AND evaluation_set_id = %s
            """
            tag_result = execute_query(
                tag_query, (pkl_id, evaluation_set_id), fetch_one=True
            )
        else:
            tag_query = """
            SELECT tag
            FROM dlp_tags
            WHERE pkl_id = %s
            """
            tag_result = execute_query(tag_query, (pkl_id,), fetch_one=True)
        tag = ""
        # print(tag_result)
        if tag_result["success"] and tag_result["data"]:
            tag = tag_result["data"][0] or ""

        # 准备返回数据,同时包含路径点坐标
        paths_info = []

        if ego_gt is not None and ego_gt_mask is not None and ego_gt_mask[-1] >= 0.5:
            if len(ego_gt) > 50:
                step = max(1, len(ego_gt) // 50)
                # ego_gt0 = ego_gt[:4]
                ego_gt = ego_gt[::step]
                # ego_gt = ego_gt0 + ego_gt1
            gt_visualization_points = []
            gt_middle_point = None
            for i, (point, mask) in enumerate(zip(ego_gt, ego_gt_mask)):
                if mask >= 0.5:  # 有效点
                    if i == ego_final_index:
                        gt_middle_point = [float(point[0]), float(point[1]), 0.0]
                    gt_visualization_points.append(
                        [float(point[0]), float(point[1]), 0.0]
                    )
            # gt_visualization_points 下采样到50个点 当前200个

            # print('..gt path',gt_visualization_points[-1][0],gt_visualization_points[-1][1])
            if len(gt_visualization_points) > 0:
                gt_path_info = {
                    "index": -1,  # GT路径使用-1作为索引
                    "probability": 1.0,  # GT路径概率设为1.0
                    "points_count": len(gt_visualization_points),
                    "annotation": annotations.get(-1, None),  # 查找GT路径的标注
                    "visualization_points": gt_visualization_points,
                    "middle_point": gt_middle_point,
                    "is_ground_truth": True,  # 标识为GT路径
                }
                paths_info.append(gt_path_info)
        for i, path in enumerate(pdp_paths):
            middle_point = None
            prob = float(path.get("prob", 0))
            # if prob == 0.0:
            #     continue
            raw_points = path.get("raw_points", [])
            points_count = len(raw_points)

            # 提取可视化所需的点数据
            visualization_points = []
            index = 0
            for point in raw_points:
                if index == ego_final_index:
                    middle_point = point
                # 确保每个点至少有x,y坐标
                if len(point) >= 2:
                    # 将点转换为[x,y,z]格式,如果没有z坐标则默认为0
                    visualization_point = [
                        float(point[0]),
                        float(point[1]),
                        float(point[2]) if len(point) > 2 else 0.0,
                    ]
                    visualization_points.append(visualization_point)
                index += 1

            path_info = {
                "index": i,
                "probability": prob,
                "points_count": points_count,
                "annotation": None,
                "visualization_points": visualization_points,  # 添加可视化点数据
                "middle_point": middle_point,
                "is_ground_truth": False,  # 标识为非GT路径
            }
            paths_info.append(path_info)
            # break #only first path

        # 处理DLP轨迹数据
        trajs_info = []
        for i, traj in enumerate(pdp_trajs[:6]):
            prob = float(traj.get("prob", 0))
            vel = traj.get("vel", [])
            acc = traj.get("acc", [])
            s = traj.get("s", [])[2:]

            # 确保轨迹数据为列表格式
            if isinstance(vel, np.ndarray):
                vel = vel.tolist()
            if isinstance(acc, np.ndarray):
                acc = acc.tolist()
            if isinstance(s, np.ndarray):
                s = s.tolist()

            traj_info = {
                "index": i,
                "probability": prob,
                "vel": vel,
                "acc": acc,
                "s": s,
                "annotation": annotations.get(i, None),
                # "is_ground_truth": False,  # 标识为非GT轨迹
            }
            trajs_info.append(traj_info)
        # 查询该 PKL 文件的选中对象
        selected_objects_query = """
        SELECT selected_object_ids FROM dlp_selected_objects WHERE pkl_id = %s
        """
        selected_objects_result = execute_query(
            selected_objects_query, (pkl_id,), fetch_one=True
        )

        selected_object_ids = []
        if selected_objects_result["success"] and selected_objects_result["data"]:
            try:
                selected_object_ids = (
                    json.loads(selected_objects_result["data"][0])
                    if selected_objects_result["data"][0]
                    else []
                )
                # print(selected_object_ids)
            except (json.JSONDecodeError, TypeError):
                selected_object_ids = []
        # print('ego traj',ego_traj_length)
        future_obj_infos = {}

        if future_obj is not None:
            obj_length = data["feature_obj"][:, 5, 9]
            future_obj = np.array(future_obj)
            future_obj_mask = np.array(future_obj_mask)
            chosen_path = np.array(pdp_paths[0]["raw_points"])
            valid = np.any(future_obj_mask, axis=1)
            valid_obj_indices = np.where(valid)[0]
            # 提取有效轨迹和对应的掩码
            valid_future_obj = future_obj[valid_obj_indices]  # 形状 (有效轨迹数, 12, 2)
            valid_future_obj_mask = future_obj_mask[
                valid_obj_indices
            ]  # 形状 (有效轨迹数, 12)
            valid_obj_length = obj_length[valid_obj_indices]
            projected_valid_future_obj, _ = project_valid_trajectories_to_path(
                valid_future_obj, valid_future_obj_mask, chosen_path, valid_obj_length
            )  # (有效轨迹数, 12,有效点数, 2)
            print(len(projected_valid_future_obj))
            for i, projected_s_with_ts in enumerate(projected_valid_future_obj):
                ts = projected_s_with_ts[:, 0]
                s = projected_s_with_ts[:, 1]
                vel = projected_s_with_ts[:, 2]
                future_obj_infos[int(valid_obj_indices[i])] = {
                    "timestamp": ts.tolist(),
                    "projected_s": s.tolist(),
                    "projected_vel": vel.tolist(),
                }
        return {
            "success": True,
            "paths": paths_info,
            "trajs": trajs_info,
            "total": len(paths_info),
            "is_dirty": is_dirty,
            "latest_annotation_info": latest_annotation_info,  # 添加最新的标注信息
            "ego_traj_length": ego_traj_length.tolist(),
            "ego_traj_vel": ego_traj_vel.tolist(),
            "selected_object_ids": selected_object_ids,
            "future_objects_info": future_obj_infos,
            "tag": tag,  # 添加标签信息
        }

    # except Exception as e:
    #     raise HTTPException(status_code=500, detail=f"处理PKL文件时出错: {str(e)}")


@router.post("/dlp-paths")
async def annotate_dlp_path(
    annotation: DlpPathAnnotation,
    current_user: Optional[dict] = Depends(get_current_user),
):
    """
    标注DLP轨迹（新增或更新）或删除标注
    """
    # 验证标注值
    if annotation.annotation not in ["0", "1", "2", "3", "4", "5"]:
        raise HTTPException(
            status_code=400, detail="标注值必须是'0'、'1'、'2'、'3'、'4'或'5'"
        )

    # 检查pkl_id是否存在
    check_query = "SELECT id FROM evaluation_case_pool WHERE id = %s"
    check_result = execute_query(check_query, (annotation.pkl_id,), fetch_one=True)

    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="指定的PKL ID不存在")

    # 如果是删除操作
    if annotation.delete_annotation:
        delete_query = """
        DELETE FROM dlp_path_annotation
        WHERE pkl_id = %s AND traj_index = %s AND inference_config_id = %s AND evaluation_set_id = %s
        """
        params = (
            annotation.pkl_id,
            annotation.traj_index,
            annotation.inference_config_id,
            annotation.evaluation_set_id,
        )
        result = execute_query(delete_query, params)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"删除标注时出错: {result['error']}"
            )

        return {"success": True, "message": "标注已删除"}
    else:
        # 插入或更新标注
        if current_user:
            query = """
            INSERT INTO dlp_path_annotation
            (pkl_id, traj_index, annotation, inference_config_id, evaluation_set_id,
                 employee_id, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON DUPLICATE KEY UPDATE
            annotation = VALUES(annotation),
            employee_id = VALUES(employee_id),
            updated_at = CURRENT_TIMESTAMP
            """
            params = (
                annotation.pkl_id,
                annotation.traj_index,
                annotation.annotation,
                annotation.inference_config_id,
                annotation.evaluation_set_id
                if annotation.evaluation_set_id is not None
                else None,
                current_user.get("employee_id", ""),
            )
        else:
            query = """
            INSERT INTO dlp_path_annotation
            (pkl_id, traj_index, annotation, inference_config_id, evaluation_set_id,
             created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON DUPLICATE KEY UPDATE
            annotation = VALUES(annotation),
            updated_at = CURRENT_TIMESTAMP
            """
            params = (
                annotation.pkl_id,
                annotation.traj_index,
                annotation.annotation,
                annotation.inference_config_id,
                annotation.evaluation_set_id
                if annotation.evaluation_set_id is not None
                else None,
            )

        result = execute_query(query, params)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"保存标注时出错: {result['error']}"
            )

        return {"success": True, "message": "标注保存成功"}


@router.post("/dlp-selected-objects")
async def save_dlp_selected_objects(
    request: DLPSelectedObjects,
    current_user: Optional[dict] = Depends(get_current_user),
):
    """保存 PKL 文件的选中对象列表"""
    # 检查pkl_id是否存在
    check_query = "SELECT id FROM evaluation_case_pool WHERE id = %s"
    check_result = execute_query(check_query, (request.pkl_id,), fetch_one=True)
    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="指定的PKL ID不存在")
    # 如果选中的对象为空，则删除记录
    if not request.selected_object_ids:
        delete_query = "DELETE FROM dlp_selected_objects WHERE pkl_id = %s"
        result = execute_query(delete_query, (request.pkl_id,))
        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"删除空选中对象记录时出错: {result['error']}"
            )
        return {"success": True, "message": "空选中对象记录已删除"}
    if current_user:
        query = """
        INSERT INTO dlp_selected_objects
        (pkl_id, selected_object_ids, evaluation_set_id, employee_id, updated_at)
        VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
        ON DUPLICATE KEY UPDATE
        selected_object_ids = VALUES(selected_object_ids),
        updated_at = CURRENT_TIMESTAMP
        """

        params = (
            request.pkl_id,
            json.dumps(request.selected_object_ids),
            request.evaluation_set_id
            if request.evaluation_set_id is not None
            else None,
            current_user.get("employee_id", ""),
        )
    else:
        query = """
        INSERT INTO dlp_selected_objects
        (pkl_id, selected_object_ids, evaluation_set_id, employee_id, updated_at)
        VALUES (%s, %s, %s, CURRENT_TIMESTAMP)
        ON DUPLICATE KEY UPDATE
        selected_object_ids = VALUES(selected_object_ids),
        updated_at = CURRENT_TIMESTAMP
        """

        params = (
            request.pkl_id,
            json.dumps(request.selected_object_ids),
            request.evaluation_set_id
            if request.evaluation_set_id is not None
            else None,
        )
    result = execute_query(query, params)

    if not result["success"]:
        raise HTTPException(
            status_code=500, detail=f"保存选中对象时出错: {result['error']}"
        )

    return {"success": True, "message": "选中对象保存成功"}


# 新增保存标签的API
@router.post("/save-dlp-tag")
async def save_dlp_tag(
    request: SaveTagRequest,
    current_user: Optional[dict] = Depends(get_current_user),
):
    """
    保存PKL文件的标签
    """
    # 检查pkl_id是否存在
    check_query = "SELECT id FROM evaluation_case_pool WHERE id = %s"
    check_result = execute_query(check_query, (request.pkl_id,), fetch_one=True)

    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="指定的PKL ID不存在")

    # 插入或更新标签
    if current_user:
        query = """
        INSERT INTO dlp_tags
        (pkl_id, tag, evaluation_set_id, employee_id, updated_at)
        VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
        ON DUPLICATE KEY UPDATE
        tag = VALUES(tag),
        employee_id = VALUES(employee_id),
        updated_at = CURRENT_TIMESTAMP
        """
        params = (
            request.pkl_id,
            request.tag,
            request.evaluation_set_id,
            current_user.get("employee_id", ""),
        )
    else:
        query = """
        INSERT INTO dlp_tags
        (pkl_id, tag, evaluation_set_id, updated_at)
        VALUES (%s, %s, %s, CURRENT_TIMESTAMP)
        ON DUPLICATE KEY UPDATE
        tag = VALUES(tag),
        updated_at = CURRENT_TIMESTAMP
        """
        params = (
            request.pkl_id,
            request.tag,
            request.evaluation_set_id,
        )

    result = execute_query(query, params)

    if not result["success"]:
        raise HTTPException(
            status_code=500, detail=f"保存标签时出错: {result['error']}"
        )

    return {"success": True, "message": "标签保存成功"}


@router.get("/export-annotations")
async def export_annotations(
    annotation_type: Optional[str] = Query(
        None, description="标注类型过滤，可选值：0、1、2、3、4、5"
    ),
    inference_config_id: Optional[int] = Query(None, description="推理配置ID过滤"),
):
    """
    导出所有DLP标注结果，以pkl_dir+pkl_name为索引

    参数:
    - annotation_type: 可选，按标注类型过滤 (0, 1, 2, 3, 4, 5)
    - inference_config_id: 可选，按推理配置ID过滤

    返回:
    - 以{pkl_path: [标注结果]} 格式的数据
    """
    try:
        # 构建查询条件
        query_conditions = []
        params = []

        if annotation_type:
            if annotation_type not in ["0", "1", "2", "3", "4", "5"]:
                raise HTTPException(
                    status_code=400,
                    detail="标注类型必须是 '0', '1', '2', '3', '4' 或 '5'",
                )
            query_conditions.append("a.annotation = %s")
            params.append(annotation_type)

        if inference_config_id is not None:
            query_conditions.append("a.inference_config_id = %s")
            params.append(inference_config_id)

        # 构建WHERE子句
        where_clause = ""
        if query_conditions:
            where_clause = "WHERE " + " AND ".join(query_conditions)

        # 查询标注和对应的pickle文件路径
        query = f"""
        SELECT
            CONCAT(p.pkl_dir, '/', p.pkl_name) as pickle_path,
            a.traj_index,
            a.annotation,
            a.inference_config_id,
            a.updated_at,
            a.employee_id
        FROM
            dlp_path_annotation a
        JOIN
            evaluation_case_pool p ON a.pkl_id = p.id
        {where_clause}
        ORDER BY
            pickle_path, a.traj_index
        """

        result = execute_query(query, params, fetch_all=True)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"查询标注数据时出错: {result['error']}"
            )

        # 组织返回数据，按pickle_path分组
        annotations_by_path = {}

        for row in result["data"]:
            (
                pickle_path,
                traj_index,
                annotation,
                inference_config_id,
                updated_at,
                employee_id,
            ) = row

            # 确保路径存在于字典中
            if pickle_path not in annotations_by_path:
                annotations_by_path[pickle_path] = []
            # 添加标注信息
            annotations_by_path[pickle_path].append(
                {
                    "traj_index": traj_index,
                    "annotation": annotation,
                    "inference_config_id": inference_config_id,
                }
            )
        return {
            "success": True,
            "total": len(result["data"]),
            "annotation_count": len(annotations_by_path),
            "annotations": annotations_by_path,
        }

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"导出标注数据时出错: {str(e)}")


@router.get("/export-dlp-annotations/{evaluation_set_id}")
async def export_dlp_annotations_by_set(
    evaluation_set_id: int = Path(..., description="评测集ID"),
):
    """
    导出指定评测集下的DLP标注结果，以pkl_dir+pkl_name为索引，同时包含检查状态和标签信息

    参数:
    - evaluation_set_id: 路径参数，评测集ID

    返回:
    - 以{pkl_path: [标注结果]} 格式的数据，每条路径包含is_checked字段和tag信息
    """
    try:
        # 检查评测集ID是否存在
        check_set_query = "SELECT id FROM evaluation_set WHERE id = %s"
        set_exists_result = execute_query(
            check_set_query, (evaluation_set_id,), fetch_one=True
        )
        if not set_exists_result["success"] or not set_exists_result["data"]:
            raise HTTPException(
                status_code=404, detail=f"评测集ID {evaluation_set_id} 不存在"
            )

        # 修改查询，添加检查状态信息和标签信息
        query = """
        SELECT
            CONCAT(p.pkl_dir, '/', p.pkl_name) as pickle_path,
            a.traj_index,
            a.annotation,
            a.inference_config_id,
            a.updated_at,
            a.employee_id,
            COALESCE(escp.is_checked, FALSE) as is_checked,
            escp.checked_at,
            escp.checked_by,
            dso.selected_object_ids,
            dt.tag,  -- 添加标签字段
            p.id as pkl_id,  -- 添加pkl_id用于后续处理
            p.vin,  -- 添加VIN信息
            p.vehicle_type  -- 添加车型信息
        FROM
            dlp_path_annotation a
        JOIN
            evaluation_case_pool p ON a.pkl_id = p.id
        LEFT JOIN
            evaluation_set_case_pool escp ON a.pkl_id = escp.evaluation_case_id
            AND escp.evaluation_set_id = %s
        LEFT JOIN
            dlp_selected_objects dso ON a.pkl_id = dso.pkl_id
        LEFT JOIN
            dlp_tags dt ON a.pkl_id = dt.pkl_id 
            AND dt.evaluation_set_id = %s  -- 确保获取对应评测集的标签
        WHERE
            a.evaluation_set_id = %s
        ORDER BY
            pickle_path, a.traj_index
        """

        result = execute_query(
            query,
            (evaluation_set_id, evaluation_set_id, evaluation_set_id),
            fetch_all=True,
        )

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"查询标注数据时出错: {result['error']}"
            )

        # 组织返回数据，按pickle_path分组
        annotations_by_path = {}

        for row in result["data"]:
            (
                pickle_path,
                traj_index,
                annotation,
                db_inference_config_id,
                updated_at,
                employee_id,
                is_checked,
                checked_at,
                checked_by,
                select_objected_ids,
                tag,  # 新增标签字段
                pkl_id,  # 新增pkl_id
                vin,  # 新增VIN
                vehicle_type,  # 新增车型
            ) = row

            id_list = []
            # transfer objected_ids to list
            if select_objected_ids is not None:
                select_objected_ids = json.loads(select_objected_ids)
                id_list = [int(id.split("_")[1]) for id in select_objected_ids]

            # 确保路径存在于字典中
            if pickle_path not in annotations_by_path:
                annotations_by_path[pickle_path] = {
                    "pkl_id": pkl_id,  # 添加pkl_id
                    "vin": vin,  # 添加VIN信息
                    "vehicle_type": vehicle_type,  # 添加车型信息
                    "tag": tag,  # 添加标签信息
                    "metadata": {
                        "last_updated": None,
                        "last_updated_by": None,
                    },
                    "annotated_5": 0,
                    "selected_object_ids": id_list,
                    "annotations": [],  # 将标注数据放在annotations数组中
                }

            # 添加标注信息，包含检查状态
            annotations_by_path[pickle_path]["annotations"].append(
                {
                    "traj_index": traj_index,
                    "annotation": annotation,
                    "inference_config_id": db_inference_config_id,
                    "is_checked": bool(is_checked),  # 确保是布尔值
                    "checked_at": checked_at.isoformat() if checked_at else None,
                    "checked_by": checked_by,
                }
            )
            if annotation == "5":
                annotations_by_path[pickle_path]["annotated_5"] += 1
            # 更新最近更新时间和人员
            if updated_at and (
                not annotations_by_path[pickle_path]["metadata"]["last_updated"]
                or updated_at.isoformat()
                > annotations_by_path[pickle_path]["metadata"]["last_updated"]
            ):
                annotations_by_path[pickle_path]["metadata"]["last_updated"] = (
                    updated_at.isoformat()
                )
                annotations_by_path[pickle_path]["metadata"]["last_updated_by"] = (
                    employee_id
                )

        # 统计信息
        total_annotations = len(result["data"])
        pkl_with_tags = sum(
            1 for data in annotations_by_path.values() if data.get("tag")
        )
        pkl_with_annotations = len(annotations_by_path)

        return {
            "success": True,
            "evaluation_set_id": evaluation_set_id,
            "export_time": datetime.now().isoformat(),
            "statistics": {
                "total_annotations": total_annotations,
                "total_pkls": pkl_with_annotations,
                "pkl_with_tags": pkl_with_tags,
            },
            "data": annotations_by_path,
        }

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"导出标注数据时出错: {str(e)}")

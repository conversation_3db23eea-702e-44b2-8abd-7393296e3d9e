# database/db_operations.py

import mysql.connector.pooling
from database.config import EVAL_DB_CONFIG
import json
import uuid
pool_config = EVAL_DB_CONFIG.copy()
if 'database' not in pool_config:
    pool_config['database'] = 'my_eval_db'

# 连接池配置
pool_config.update({
    'pool_name': 'eval_pool',
    'pool_size': 20,  # 根据实际并发量调整
    'pool_reset_session': True,
    'autocommit': True
})
# connection_pool = mysql.connector.pooling.MySQLConnectionPool(**pool_config)


def get_db_connection(database='my_eval_db'):
    """获取数据库连接"""
    config = EVAL_DB_CONFIG.copy()
    if 'database' not in config:
        config['database'] = database  # 使用默认数据库名
    return mysql.connector.connect(**config)
    # return connection_pool.get_connection()

def insert_evaluation_set(data):
    """插入单个评测集"""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # 准备插入语句
        sql = """
        INSERT INTO evaluation_case_pool 
        (pkl_name, pkl_dir, key_obs_id, path_range) 
        VALUES (%s, %s, %s, %s)
        """

        # 路径范围需要转为JSON字符串
        path_range_json = json.dumps(data.get('path_range', [0, 10]))

        # 执行插入
        cursor.execute(sql, (
            data.get('pkl_name', ''),
            data.get('pkl_dir', ''),
            data.get('key_obs_id', 0),
            path_range_json
        ))

        # 获取插入ID
        insert_id = cursor.lastrowid
        conn.commit()
        return {"success": True, "id": insert_id}

    except mysql.connector.Error as err:
        conn.rollback()
        return {"success": False, "error": str(err)}
    finally:
        cursor.close()
        conn.close()


# 修改 database/db_operations.py 中的 insert_many_evaluation_case_pool 函数


def insert_many_evaluation_case_pool(data_list):
    """
    批量插入评测集数据，使用pkl_name作为唯一约束
    """
    if not data_list:
        return {"success": False, "error": "没有数据可插入"}

    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 1. 使用 pkl_name 作为唯一键进行去重
        seen_keys = set()
        unique_data = []
        duplicate_info = []
        
        for i, item in enumerate(data_list):
            # 使用 pkl_name 作为唯一键
            unique_key = item.get('pkl_name', '')
            
            if unique_key in seen_keys:
                duplicate_info.append({
                    'index': i,
                    'pkl_name': item.get('pkl_name', ''),
                    'vin': item.get('vin', ''),
                    'time_ns': item.get('time_ns', ''),
                    'reason': '本批次内重复（pkl_name）'
                })
                continue
                
            seen_keys.add(unique_key)
            unique_data.append({
                'data': item,
                'original_index': i
            })

        print(f'批次去重（按pkl_name）：原始 {len(data_list)} 条 -> 唯一 {len(unique_data)} 条')

        # 2. 处理去重后的数据
        values = []
        temp_uuids = []
        
        for item_info in unique_data:
            item = item_info['data']
            temp_uuid = str(uuid.uuid4())
            temp_uuids.append(temp_uuid)

            path_range = [item.get('path_range_start', 0),
                          item.get('path_range_end', 10)]

            values.append((
                item['vehicle_type'],
                item['time_ns'],
                item['pkl_dir'],
                item.get('key_obs_id', 0),
                json.dumps(path_range),
                temp_uuid,
                item['vin'],
                item['pkl_name']
            ))

        # 3. 执行插入
        if values:
            insert_query = """
            INSERT INTO evaluation_case_pool 
            (vehicle_type, time_ns, pkl_dir, key_obs_id, path_range, insert_uuid, vin, pkl_name)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                vehicle_type = VALUES(vehicle_type),
                time_ns = VALUES(time_ns),
                pkl_dir = VALUES(pkl_dir),
                key_obs_id = VALUES(key_obs_id),
                vin = VALUES(vin),
                path_range = VALUES(path_range),
                insert_uuid = VALUES(insert_uuid)
            """
            
            cursor.executemany(insert_query, values)
            affected_rows = cursor.rowcount
            conn.commit()

            print(f'数据库操作：尝试处理 {len(values)} 条，受影响行数 {affected_rows}')

            # 4. 反查ID
            format_strings = ','.join(['%s'] * len(temp_uuids))
            select_query = f"""
            SELECT id, insert_uuid FROM evaluation_case_pool
            WHERE insert_uuid IN ({format_strings})
            """
            cursor.execute(select_query, tuple(temp_uuids))
            results = cursor.fetchall()

            uuid_to_id = {uuid_val: id_val for id_val, uuid_val in results}
            inserted_ids = [uuid_to_id.get(uuid_val) for uuid_val in temp_uuids]
            valid_ids = [id_val for id_val in inserted_ids if id_val is not None]

            # 5. 清理临时UUID
            if valid_ids:
                cleanup_query = f"""
                UPDATE evaluation_case_pool
                SET insert_uuid = NULL
                WHERE id IN ({','.join(['%s'] * len(valid_ids))})
                """
                cursor.execute(cleanup_query, tuple(valid_ids))
                conn.commit()

            return {
                "success": True,
                "count": len(valid_ids),
                "ids": valid_ids,
                "total_input": len(data_list),
                "unique_processed": len(unique_data),
                "batch_duplicates": len(duplicate_info),
                "constraint_used": "pkl_name",
                "message": f"输入{len(data_list)}条，按pkl_name去重后{len(unique_data)}条，成功处理{len(valid_ids)}条"
            }
        else:
            return {
                "success": True,
                "count": 0,
                "ids": [],
                "total_input": len(data_list),
                "unique_processed": 0,
                "batch_duplicates": len(data_list),
                "constraint_used": "pkl_name",
                "message": "所有数据在批次内都重复"
            }

    except Exception as e:
        print(f"插入数据时出错: {e}")
        if conn:
            conn.rollback()
        return {
            "success": False, 
            "error": str(e),
            "message": f"数据插入失败: {str(e)}"
        }

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


def get_evaluation_sets_with_pagination(page=1, per_page=12, search=None):
    """获取分页的评测集数据，支持标签过滤和名称搜索"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=False)  # 使用元组模式获取结果

    try:
        # 构建基础查询
        sql = "SELECT * FROM evaluation_case_pool"
        # 给COUNT结果命名
        count_sql = "SELECT COUNT(*) as total FROM evaluation_case_pool"
        conditions = []
        params = []


        # 添加搜索条件
        if search and search.strip():
            conditions.append("pkl_name LIKE %s")
            params.append(f"%{search}%")

        # 组合WHERE子句
        if conditions:
            sql += " WHERE " + " AND ".join(conditions)
            count_sql += " WHERE " + " AND ".join(conditions)

        # 获取总记录数
        cursor.execute(count_sql, params)
        result = cursor.fetchone()
        # 安全地获取总数，避免None
        total_count = result[0] if result else 0

        # 添加分页
        offset = (page - 1) * per_page
        sql += " ORDER BY id DESC LIMIT %s OFFSET %s"
        params.extend([per_page, offset])

        # 执行查询
        cursor = conn.cursor(dictionary=True)  # 切换到字典模式获取记录
        cursor.execute(sql, params)
        results = cursor.fetchall()

        # 解析JSON格式的path_range
        for row in results:
            if 'path_range' in row and row['path_range']:
                try:
                    row['path_range'] = json.loads(row['path_range'])
                except (json.JSONDecodeError, TypeError):
                    row['path_range'] = [0, 10]  # 默认值

        return {
            "success": True,
            "data": results,
            "total": total_count,
            "page": page,
            "per_page": per_page,
            "pages": max(1, (total_count + per_page - 1) // per_page)  # 至少有1页
        }

    except mysql.connector.Error as err:
        print(f"数据库错误: {err}")
        return {"success": False, "error": str(err)}
    finally:
        cursor.close()
        conn.close()


def execute_query(query, params=None, fetch_one=False, fetch_all=False, get_last_id=False, return_dict=False, execute_many=False, transaction=False):
    """
    执行通用SQL查询，支持批量操作
    
    Args:
        query: SQL查询语句
        params: 查询参数，可以是单个参数的元组/列表，或批量参数的列表
        fetch_one: 是否获取单行结果
        fetch_all: 是否获取所有结果
        get_last_id: 是否获取最后插入的ID
        return_dict: 是否以字典形式返回结果
        execute_many: 是否执行批量操作
    
    Returns:
        包含操作结果的字典
    """
    conn = get_db_connection()
    if return_dict:
        cursor = conn.cursor(dictionary=True)
    else:
        cursor = conn.cursor()
    if transaction:
        conn.start_transaction()
    try:
        if params:
            if execute_many:
                cursor.executemany(query, params)
            else:
                cursor.execute(query, params)
        else:
            cursor.execute(query)

        result = {"success": True}

        if fetch_one:
            result["data"] = cursor.fetchone()
        elif fetch_all:
            result["data"] = cursor.fetchall()

        if get_last_id:
            result["last_id"] = cursor.lastrowid

        # 同时返回受影响的行数
        result["rowcount"] = cursor.rowcount

        conn.commit()
        return result

    except Exception as err:
        conn.rollback()
        return {"success": False, "error": str(err)}
    finally:
        cursor.close()
        conn.close()


def create_evaluation_set(set_name, creator_name, description="", scene_tag=None):
    """创建一个新的评测集，支持场景标签"""

    # 处理 scene_tag
    scene_tag_str = None
    if scene_tag and isinstance(scene_tag, list) and len(scene_tag) > 0:
        # 将场景标签列表转换为MySQL SET格式的字符串
        scene_tag_str = ','.join(scene_tag)

    query = """
    INSERT INTO evaluation_set (set_name, creator_name, description, scene_tag)
    VALUES (%s, %s, %s, %s)
    """
    params = (set_name, creator_name, description, scene_tag_str)

    result = execute_query(query, params, get_last_id=True)
    if result["success"]:
        return {"success": True, "id": result["last_id"], "message": "评测集创建成功"}
    else:
        return {"success": False, "error": result["error"]}


def add_cases_to_evaluation_case_pool(evaluation_set_id, case_ids):
    """向评测集中添加评测案例"""
    if not case_ids:
        return {"success": False, "error": "未提供案例ID"}

    # 首先检查评测集是否存在
    check_query = "SELECT id FROM evaluation_set WHERE id = %s"
    check_result = execute_query(
        check_query, (evaluation_set_id,), fetch_one=True)

    if not check_result["success"] or not check_result.get("data"):
        return {"success": False, "error": "评测集不存在"}

    # 准备批量插入
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # 构建批量插入的值
        values = [(evaluation_set_id, case_id) for case_id in case_ids]

        # 使用INSERT IGNORE避免重复插入导致的错误
        insert_query = """
        INSERT IGNORE INTO evaluation_set_case_pool 
        (evaluation_set_id, evaluation_case_id) 
        VALUES (%s, %s)
        """

        cursor.executemany(insert_query, values)
        conn.commit()

        return {
            "success": True,
            "count": cursor.rowcount,
            "message": f"成功添加{cursor.rowcount}个案例到评测集"
        }

    except mysql.connector.Error as err:
        conn.rollback()
        return {"success": False, "error": str(err)}
    finally:
        cursor.close()
        conn.close()


def remove_cases_from_evaluation_set(evaluation_set_id, case_ids):
    """从评测集中删除评测案例，同时删除相关的标注结果"""
    if not case_ids:
        return {"success": False, "error": "未提供案例ID"}

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # 开始事务
        conn.start_transaction()

        # 1. 删除相关的pdp_path_annotation标注结果
        annotation_placeholders = ', '.join(['%s'] * len(case_ids))
        delete_annotation_query = f"""
        DELETE FROM pdp_path_annotation 
        WHERE pkl_id IN ({annotation_placeholders})
        """
        cursor.execute(delete_annotation_query, case_ids)
        annotation_deleted_count = cursor.rowcount

        # 2. 删除evaluation_set_case_pool中的记录
        case_pool_placeholders = ', '.join(['%s'] * len(case_ids))
        delete_case_pool_query = f"""
        DELETE FROM evaluation_set_case_pool 
        WHERE evaluation_set_id = %s AND evaluation_case_id IN ({case_pool_placeholders})
        """
        
        # 准备参数
        params = [evaluation_set_id] + case_ids
        cursor.execute(delete_case_pool_query, params)
        case_pool_deleted_count = cursor.rowcount

        # 提交事务
        conn.commit()

        return {
            "success": True,
            "case_pool_count": case_pool_deleted_count,
            "annotation_count": annotation_deleted_count,
            "message": f"成功从评测集中移除{case_pool_deleted_count}个案例，删除{annotation_deleted_count}条标注结果"
        }

    except mysql.connector.Error as err:
        conn.rollback()
        return {"success": False, "error": str(err)}
    finally:
        cursor.close()
        conn.close()


def delete_evaluation_set(evaluation_set_id):
    """删除指定ID的评测集"""
    # 使用外键级联删除，所以只需要删除评测集本身
    query = "DELETE FROM evaluation_set WHERE id = %s"
    params = (evaluation_set_id,)

    result = execute_query(query, params)
    if result["success"]:
        return {"success": True, "message": "评测集删除成功"}
    else:
        return {"success": False, "error": result["error"]}


def get_evaluation_sets(page=1, per_page=10, name=None, creator=None):
    """
    获取评测集列表，支持按名称和创建人过滤
    返回分页结果、总记录数和每个评测集包含的案例数量
    """
    conn = get_db_connection()

    try:
        # 构建基础查询
        query = "SELECT * FROM evaluation_set"
        count_query = "SELECT COUNT(*) as total FROM evaluation_set"

        conditions = []
        params = []

        # 添加过滤条件
        if name:
            conditions.append("set_name LIKE %s")
            params.append(f"%{name}%")

        if creator:
            conditions.append("creator_name LIKE %s")
            params.append(f"%{creator}%")

        # 组合WHERE子句
        if conditions:
            where_clause = " WHERE " + " AND ".join(conditions)
            query += where_clause
            count_query += where_clause

        # 获取总记录数
        cursor = conn.cursor(dictionary=True)
        cursor.execute(count_query, params)
        result = cursor.fetchone()
        total_count = result['total'] if result else 0

        # 添加分页和排序
        query += " ORDER BY created_at DESC LIMIT %s OFFSET %s"
        offset = (page - 1) * per_page
        params.extend([per_page, offset])

        # 执行主查询
        cursor.execute(query, params)
        results = cursor.fetchall()

        # 为每个评测集获取包含的案例数量
        for item in results:
            # 查询每个评测集中的案例数量
            case_count_query = """
            SELECT COUNT(*) as count 
            FROM evaluation_set_case_pool 
            WHERE evaluation_set_id = %s
            """
            cursor.execute(case_count_query, (item['id'],))
            count_result = cursor.fetchone()
            item['case_count'] = count_result['count'] if count_result else 0

            # 将日期时间转换为字符串，确保JSON兼容性
            if 'created_at' in item and item['created_at']:
                item['created_at'] = item['created_at'].isoformat()
            if 'updated_at' in item and item['updated_at']:
                item['updated_at'] = item['updated_at'].isoformat()

        # 计算总页数
        total_pages = max(1, (total_count + per_page - 1) // per_page)

        return {
            "success": True,
            "data": results,
            "total": total_count,
            "page": page,
            "per_page": per_page,
            "pages": total_pages
        }

    except mysql.connector.Error as err:
        return {"success": False, "error": str(err)}
    finally:
        cursor.close()
        conn.close()


def get_evaluation_set_with_cases(evaluation_set_id, page=1, per_page=10):
    """
    获取指定ID的评测集详情，包括所有关联的评测案例，支持分页
    """
    conn = get_db_connection()

    try:
        # 获取评测集基本信息
        set_cursor = conn.cursor(dictionary=True)
        set_query = "SELECT * FROM evaluation_set WHERE id = %s"
        set_cursor.execute(set_query, (evaluation_set_id,))
        evaluation_set = set_cursor.fetchone()

        if not evaluation_set:
            return {"success": False, "error": "评测集不存在"}

        # 获取关联的评测案例总数
        count_cursor = conn.cursor(dictionary=True)
        count_query = """
        SELECT COUNT(*) as total
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id
        WHERE escp.evaluation_set_id = %s
        """
        count_cursor.execute(count_query, (evaluation_set_id,))
        total_count = count_cursor.fetchone()['total']

        # 获取关联的评测案例（带分页）
        cases_cursor = conn.cursor(dictionary=True)
        offset = (page - 1) * per_page
        cases_query = """
        SELECT ecp.* 
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id
        WHERE escp.evaluation_set_id = %s
        ORDER BY ecp.id DESC
        LIMIT %s OFFSET %s
        """
        cases_cursor.execute(
            cases_query, (evaluation_set_id, per_page, offset))
        cases = cases_cursor.fetchall()

        # 解析JSON格式的path_range
        for case in cases:
            if 'path_range' in case and case['path_range']:
                try:
                    case['path_range'] = json.loads(case['path_range'])
                except (json.JSONDecodeError, TypeError):
                    case['path_range'] = [0, 10]  # 默认值

        # 计算总页数
        total_pages = max(1, (total_count + per_page - 1) // per_page)

        # 组合结果
        return {
            "success": True,
            "evaluation_set": evaluation_set,
            "cases": cases,
            "case_count": total_count,
            "page": page,
            "per_page": per_page,
            "pages": total_pages
        }

    except mysql.connector.Error as err:
        return {"success": False, "error": str(err)}
    finally:
        if 'set_cursor' in locals():
            set_cursor.close()
        if 'count_cursor' in locals():
            count_cursor.close()
        if 'cases_cursor' in locals():
            cases_cursor.close()
        conn.close()


def initialize_system():
    print("正在初始化系统...")

    # 获取所有现有评测案例的ID
    all_cases = get_all_evaluation_case_ids()
    if not all_cases["success"]:
        print(f"获取评测案例失败: {all_cases['error']}")
        return False
    return True

# 添加辅助函数到 database/db_operations.py


def get_all_evaluation_case_ids():
    """获取所有评测案例ID"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT id FROM evaluation_case_pool")
        rows = cursor.fetchall()
        case_ids = [row[0] for row in rows]

        return {"success": True, "ids": case_ids}
    except Exception as e:
        return {"success": False, "error": str(e)}
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


path_class_map = {
    "PATH_CLASS_0": 0,
    "PATH_CLASS_1": 1,
    "PATH_CLASS_2": 2,
    "PATH_CLASS_3": 3,
    "PATH_CLASS_4": 4,
    "PATH_CLASS_5": 5,
    "PATH_CLASS_6": 6,
}
dlp_class_map = {
    "DLP_CLASS_0": 0,
    "DLP_CLASS_1": 1,
    "DLP_CLASS_2": 2,
    "DLP_CLASS_3": 3,
    "DLP_CLASS_4": 4,
    "DLP_CLASS_5": 5,
    "DLP_CLASS_6": 6,
}
def batch_save_evaluation_results(evaluation_results):
    """
    批量保存评测结果到数据库
    
    Args:
        evaluation_results: 包含多个评测结果的列表，每个元素是一个字典，包含:
            - inference_result_id: 推理结果ID
            - evaluation_data: 评测结果数据
    
    Returns:
        包含操作结果的字典，包括成功数量和失败项
    """
    if not evaluation_results:
        return {"success": True, "count": 0, "message": "没有提供需要保存的评测结果"}

    conn = get_db_connection()
    conn.autocommit = False
    cursor = conn.cursor()

    try:
        # 初始化结果列表
        result_ids = []
        failed_items = []

        for item in evaluation_results:
            inference_result_id = item.get('inference_result_id')
            evaluation_data = item.get('evaluation_data', {})

            if not inference_result_id:
                failed_items.append({"item": item, "error": "缺少推理结果ID"})
                continue

            # 1. 先插入主表
            cursor.execute(
                "INSERT INTO evaluation_result (inference_result_id) VALUES (%s)",
                (inference_result_id,)
            )
            evaluation_id = cursor.lastrowid
            result_ids.append(evaluation_id)

            # 2. 插入 TopKMetrics 数据
            topk_metrics_types = [
                'prediction_accuracy_ade', 'prediction_accuracy_fde',
                'pathformer_accuracy_40_ade', 'pathformer_accuracy_40_fde',
                'pathformer_accuracy_200_ade', 'pathformer_accuracy_200_fde',
                'decision_accuracy_ade', 'decision_accuracy_fde',
                'pathformer_accuracy4s_ade', 'pathformer_accuracy4s_fde'
            ]

            for metric_type in topk_metrics_types:
                if metric_type in evaluation_data:
                    metric_data = evaluation_data[metric_type]
                    cursor.execute(
                        """
                        INSERT INTO evaluation_topk_metrics 
                        (evaluation_id, metric_type, top_1, top_3, top_6, count) 
                        VALUES (%s, %s, %s, %s, %s, %s)
                        """,
                        (
                            evaluation_id,
                            metric_type,
                            metric_data.get('top_1'),
                            metric_data.get('top_3'),
                            metric_data.get('top_6'),
                            metric_data.get('count', 0)
                        )
                    )

            # 3. 插入 TopKBoolMetrics 数据
            topk_bool_metrics_types = ['static_4s_collision']

            for metric_type in topk_bool_metrics_types:
                if metric_type in evaluation_data:
                    metric_data = evaluation_data[metric_type]
                    cursor.execute(
                        """
                        INSERT INTO evaluation_topk_bool_metrics 
                        (evaluation_id, metric_type, top_1, top_3, top_6, count) 
                        VALUES (%s, %s, %s, %s, %s, %s)
                        """,
                        (
                            evaluation_id,
                            metric_type,
                            metric_data.get('top_1', False),
                            metric_data.get('top_3', False),
                            metric_data.get('top_6', False),
                            metric_data.get('count', 0)
                        )
                    )

            # 4. 插入 PathClassFloatMetrics 数据
            if 'nth_cls_path_res_adefde' in evaluation_data:
                for path_metric in evaluation_data['nth_cls_path_res_adefde']:
                    cursor.execute(
                        """
                        INSERT INTO evaluation_path_class_float_metrics 
                        (evaluation_id, path_class, ade, fde, count) 
                        VALUES (%s, %s, %s, %s, %s)
                        """,
                        (
                            evaluation_id,
                            path_class_map[path_metric.get('path_class', 0)],
                            path_metric.get('ade'),
                            path_metric.get('fde'),
                            path_metric.get('count', 0)
                        )
                    )

            # 5. 插入 PathClassBoolMetrics 数据
            if 'nth_cls_path_res_static_collision' in evaluation_data:
                for path_metric in evaluation_data['nth_cls_path_res_static_collision']:
                    cursor.execute(
                        """
                        INSERT INTO evaluation_path_class_bool_metrics 
                        (evaluation_id, path_class, collision, count) 
                        VALUES (%s, %s, %s, %s)
                        """,
                        (
                            evaluation_id,
                            path_class_map[path_metric.get('path_class', 0)],
                            path_metric.get('collision', False),
                            path_metric.get('count', 0)
                        )
                    )

            # 6. 插入 DlpClassFloatMetrics 数据
            if 'nth_cls_dlp_res_adefde' in evaluation_data:
                for dlp_metric in evaluation_data['nth_cls_dlp_res_adefde']:
                    cursor.execute(
                        """
                        INSERT INTO evaluation_dlp_class_float_metrics 
                        (evaluation_id, dlp_class, ade, fde, count) 
                        VALUES (%s, %s, %s, %s, %s)
                        """,
                        (
                            evaluation_id,
                            dlp_class_map[dlp_metric.get('dlp_class', 0)],
                            dlp_metric.get('ade'),
                            dlp_metric.get('fde'),
                            dlp_metric.get('count', 0)
                        )
                    )

            # 7. 插入 DlpClassBoolMetrics 数据
            if 'nth_cls_dlp_res_dynamic_collision' in evaluation_data:
                for dlp_metric in evaluation_data['nth_cls_dlp_res_dynamic_collision']:
                    cursor.execute(
                        """
                        INSERT INTO evaluation_dlp_class_bool_metrics 
                        (evaluation_id, dlp_class, collision, count) 
                        VALUES (%s, %s, %s, %s)
                        """,
                        (
                            evaluation_id,
                            dlp_class_map[dlp_metric.get('dlp_class', 0)],
                            dlp_metric.get('collision', False),
                            dlp_metric.get('count', 0)
                        )
                    )

        # 提交事务
        conn.commit()

        return {
            "success": True,
            "count": len(result_ids),
            "message": f"成功保存{len(result_ids)}条评测结果",
            "result_ids": result_ids,
            "failed_items": failed_items if failed_items else None
        }

    except Exception as e:
        conn.rollback()
        return {
            "success": False,
            "error": str(e)
        }
    finally:
        cursor.close()
        conn.close()


def get_evaluation_results_by_inference_ids(inference_result_ids, top_k=None):
    """
    批量获取多个推理结果ID对应的评测结果，并支持筛选top_k值
    
    Args:
        inference_result_ids: 推理结果ID列表
        top_k: 指定要获取的top_k值，可选值为1、3、6，如果为None则获取所有值
    
    Returns:
        包含操作结果的字典，成功时包含按inference_result_id索引的评测结果
    """
    if not inference_result_ids:
        return {
            "success": False,
            "error": "未提供推理结果ID"
        }

    # 验证top_k参数
    if top_k is not None and top_k not in [1, 3, 6]:
        return {
            "success": False,
            "error": "top_k参数必须为1、3、6之一"
        }

    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # 构建查询条件
        placeholders = ','.join(['%s'] * len(inference_result_ids))

        # 查询主表
        main_query = f"""
        SELECT id, inference_result_id, created_at 
        FROM evaluation_result 
        WHERE inference_result_id IN ({placeholders})
        """
        cursor.execute(main_query, tuple(inference_result_ids))
        main_results = cursor.fetchall()

        if not main_results:
            return {
                "success": False,
                "error": "未找到对应的评测结果"
            }

        # 创建结果映射
        evaluation_ids = [result['id'] for result in main_results]
        id_to_inference_id = {
            result['id']: result['inference_result_id'] for result in main_results}
        results_map = {result['inference_result_id']: {
            "id": result['id'], "inference_result_id": result['inference_result_id']} for result in main_results}

        # 确定要获取的列
        top_k_columns = ""
        if top_k == 1:
            top_k_columns = "top_1"
        elif top_k == 3:
            top_k_columns = "top_3"
        elif top_k == 6:
            top_k_columns = "top_6"
        else:
            top_k_columns = "top_1, top_3, top_6"

        # 构建评估ID的占位符
        eval_placeholders = ','.join(['%s'] * len(evaluation_ids))

        # 查询 TopKMetrics 数据
        metrics_query = f"""
        SELECT evaluation_id, metric_type, {top_k_columns}, count 
        FROM evaluation_topk_metrics 
        WHERE evaluation_id IN ({eval_placeholders})
        """
        cursor.execute(metrics_query, tuple(evaluation_ids))
        metrics_results = cursor.fetchall()

        # 处理metrics结果
        for metric in metrics_results:
            eval_id = metric['evaluation_id']
            inference_id = id_to_inference_id[eval_id]
            metric_type = metric['metric_type']

            if top_k == 1:
                results_map[inference_id][metric_type] = metric['top_1']
            elif top_k == 3:
                results_map[inference_id][metric_type] = metric['top_3']
            elif top_k == 6:
                results_map[inference_id][metric_type] = metric['top_6']
            else:
                results_map[inference_id][metric_type] = {
                    'top_1': metric['top_1'],
                    'top_3': metric['top_3'],
                    'top_6': metric['top_6']
                }

            results_map[inference_id][f"{metric_type}_count"] = metric['count']

        # 查询 TopKBoolMetrics 数据
        bool_cols = "top_1" if top_k == 1 else "top_3" if top_k == 3 else "top_6" if top_k == 6 else "top_1, top_3, top_6"
        bool_query = f"""
        SELECT evaluation_id, metric_type, {bool_cols}, count 
        FROM evaluation_topk_bool_metrics 
        WHERE evaluation_id IN ({eval_placeholders}) AND metric_type = 'static_4s_collision'
        """
        cursor.execute(bool_query, tuple(evaluation_ids))
        bool_results = cursor.fetchall()

        # 处理bool结果
        for bool_metric in bool_results:
            eval_id = bool_metric['evaluation_id']
            inference_id = id_to_inference_id[eval_id]

            if top_k == 1:
                results_map[inference_id]['static_collision'] = bool_metric['top_1']
            elif top_k == 3:
                results_map[inference_id]['static_collision'] = bool_metric['top_3']
            elif top_k == 6:
                results_map[inference_id]['static_collision'] = bool_metric['top_6']
            else:
                results_map[inference_id]['static_collision'] = {
                    'top_1': bool_metric['top_1'],
                    'top_3': bool_metric['top_3'],
                    'top_6': bool_metric['top_6']
                }

            results_map[inference_id]['static_collision_count'] = bool_metric['count']

        return {
            "success": True,
            "data": results_map,
            "count": len(results_map)
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }
    finally:
        cursor.close()
        conn.close()


def insert_many_evaluation_case_pool(data_list):
    """
    批量插入评测集数据，提供详细的去重信息
    """
    if not data_list:
        return {"success": False, "error": "没有数据可插入"}

    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 1. 详细的去重分析
        unique_data = []
        duplicate_groups = {}
        seen_keys = {}
        
        for i, item in enumerate(data_list):
            # 构建唯一键
            unique_key = (
                item.get('pkl_name', ''),
                item.get('time_ns', ''),
                item.get('vin', ''),
                item.get('pkl_dir', '')
            )
            
            if unique_key in seen_keys:
                # 记录重复信息
                if unique_key not in duplicate_groups:
                    duplicate_groups[unique_key] = {
                        'first_index': seen_keys[unique_key],
                        'duplicates': []
                    }
                duplicate_groups[unique_key]['duplicates'].append({
                    'index': i,
                    'pkl_name': item.get('pkl_name', ''),
                    'time_ns': item.get('time_ns', ''),
                    'vin': item.get('vin', '')
                })
                continue
                
            seen_keys[unique_key] = i
            unique_data.append({
                'data': item,
                'original_index': i
            })

        print(f'去重分析：原始 {len(data_list)} 条 -> 唯一 {len(unique_data)} 条')
        
        # 打印重复详情
        if duplicate_groups:
            print("发现重复数据组:")
            for key, group in duplicate_groups.items():
                print(f"  键 {key}: 首次出现索引 {group['first_index']}, 重复 {len(group['duplicates'])} 次")

        # 2. 处理唯一数据
        values = []
        temp_uuids = []
        original_indices = []
        
        for item_info in unique_data:
            item = item_info['data']
            temp_uuid = str(uuid.uuid4())
            temp_uuids.append(temp_uuid)
            original_indices.append(item_info['original_index'])

            path_range = [item.get('path_range_start', 0),
                          item.get('path_range_end', 10)]

            values.append((
                item['vehicle_type'],
                item['time_ns'],
                item['pkl_dir'],
                item.get('key_obs_id', 0),
                json.dumps(path_range),
                temp_uuid,
                item['vin'],
                item['pkl_name']
            ))

        # 3. 执行插入
        if values:
            insert_query = """
            INSERT INTO evaluation_case_pool 
            (vehicle_type, time_ns, pkl_dir, key_obs_id, path_range, insert_uuid, vin, pkl_name)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                vehicle_type = VALUES(vehicle_type),
                time_ns = VALUES(time_ns),
                pkl_dir = VALUES(pkl_dir),
                key_obs_id = VALUES(key_obs_id),
                path_range = VALUES(path_range),
                insert_uuid = VALUES(insert_uuid),
                vin = VALUES(vin),
                pkl_name = VALUES(pkl_name)
            """
            
            cursor.executemany(insert_query, values)
            conn.commit()

            # 4. 反查ID
            format_strings = ','.join(['%s'] * len(temp_uuids))
            select_query = f"""
            SELECT id, insert_uuid FROM evaluation_case_pool
            WHERE insert_uuid IN ({format_strings})
            """
            cursor.execute(select_query, tuple(temp_uuids))
            results = cursor.fetchall()

            uuid_to_id = {uuid_val: id_val for id_val, uuid_val in results}
            inserted_ids = [uuid_to_id.get(uuid_val) for uuid_val in temp_uuids]
            valid_ids = [id_val for id_val in inserted_ids if id_val is not None]

            # 清理临时UUID
            if valid_ids:
                cleanup_query = f"""
                UPDATE evaluation_case_pool
                SET insert_uuid = NULL
                WHERE id IN ({','.join(['%s'] * len(valid_ids))})
                """
                cursor.execute(cleanup_query, tuple(valid_ids))
                conn.commit()
        else:
            valid_ids = []

        return {
            "success": True,
            "count": len(valid_ids),
            "ids": valid_ids,
            "total_input": len(data_list),
            "unique_processed": len(unique_data),
            "batch_duplicates": len(data_list) - len(unique_data),
            "duplicate_groups": duplicate_groups,
            "message": f"输入{len(data_list)}条，去重后{len(unique_data)}条，成功处理{len(valid_ids)}条"
        }

    except Exception as e:
        if conn:
            conn.rollback()
        return {
            "success": False, 
            "error": str(e),
            "message": "数据插入失败"
        }

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

